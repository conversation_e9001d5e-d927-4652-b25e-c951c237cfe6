"""
Textbook Agent - RAG-Based Tutor for IGNOU Study <PERSON><PERSON>
Handles textbook-related queries using RAG (Retrieval-Augmented Generation)
"""
import os
from typing import Dict, Any, List, Optional
from pathlib import Path

from langchain.schema import Document

from agents.base_agent import BaseAgent, AgentResponse
from core.vector_store import vector_store, document_processor
from core.llm_integration import PromptTemplates
from core.database import get_db_session, Document as DBDocument, DocumentChunk
from config.settings import settings
from utils.logger import logger


class TextbookAgent(BaseAgent):
    """
    Textbook Agent that acts as a RAG-based tutor using IGNOU textbooks.
    
    Capabilities:
    - Answer questions based on textbook content
    - Provide unit summaries
    - Explain concepts with examples
    - Support multilingual explanations
    - Process and index new textbook PDFs
    """
    
    def __init__(self):
        super().__init__("TextbookAgent")
        self.vector_store = vector_store
        self.document_processor = document_processor
        self.supported_languages = ["english", "hindi", "tamil"]
    
    def process_query(self, query: str, context: Dict[str, Any] = None, 
                     user_id: str = None) -> Dict[str, Any]:
        """Process textbook-related queries"""
        
        if not self._validate_input(query):
            return self._format_error_response("Invalid query provided")
        
        try:
            # Extract context information
            context = context or {}
            language = context.get("language", "english").lower()
            course = context.get("course")
            subject = context.get("subject")
            
            # Search for relevant content
            search_results = self._search_textbook_content(query, course, subject)
            
            if not search_results:
                return self._format_success_response(
                    "I couldn't find relevant information in the textbooks for your question. "
                    "Could you please rephrase your question or specify the course/subject?"
                )
            
            # Generate response using RAG
            response = self._generate_rag_response(query, search_results, language)
            
            metadata = {
                "sources": [result["metadata"] for result in search_results[:3]],
                "language": language,
                "search_results_count": len(search_results)
            }
            
            # Log interaction
            self._log_interaction(query, {"success": True}, user_id)
            
            return self._format_success_response(response, metadata)
            
        except Exception as e:
            logger.error(f"Error in textbook agent: {e}")
            return self._format_error_response(str(e))
    
    def _search_textbook_content(self, query: str, course: str = None, 
                                subject: str = None, k: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant textbook content"""
        
        # Enhance query with course/subject context if provided
        enhanced_query = query
        if course:
            enhanced_query = f"Course {course}: {query}"
        if subject:
            enhanced_query = f"Subject {subject}: {enhanced_query}"
        
        # Search in vector store
        results = self.vector_store.similarity_search(
            enhanced_query, 
            k=k, 
            score_threshold=0.3
        )
        
        # Filter by course/subject if specified
        if course or subject:
            filtered_results = []
            for result in results:
                metadata = result.get("metadata", {})
                if course and metadata.get("course", "").lower() != course.lower():
                    continue
                if subject and metadata.get("subject", "").lower() != subject.lower():
                    continue
                filtered_results.append(result)
            results = filtered_results
        
        return results
    
    def _generate_rag_response(self, query: str, search_results: List[Dict[str, Any]], 
                              language: str = "english") -> str:
        """Generate response using RAG approach"""
        
        # Prepare context from search results
        context_parts = []
        for i, result in enumerate(search_results[:3]):  # Use top 3 results
            content = result["content"]
            metadata = result.get("metadata", {})
            source_info = f"Source {i+1}"
            
            if metadata.get("course"):
                source_info += f" (Course: {metadata['course']}"
            if metadata.get("subject"):
                source_info += f", Subject: {metadata['subject']}"
            if metadata.get("page_number"):
                source_info += f", Page: {metadata['page_number']}"
            if metadata.get("course") or metadata.get("subject") or metadata.get("page_number"):
                source_info += ")"
            
            context_parts.append(f"{source_info}:\n{content}\n")
        
        context = "\n".join(context_parts)
        
        # Prepare system prompt
        system_prompt = PromptTemplates.TEXTBOOK_QA_SYSTEM
        
        if language != "english":
            system_prompt += f"\n\nIMPORTANT: Provide your response in {language.title()} language."
        
        # Prepare user prompt
        user_prompt = f"""
Based on the following textbook content, please answer the student's question.

TEXTBOOK CONTENT:
{context}

STUDENT'S QUESTION:
{query}

Please provide a clear, comprehensive answer based on the textbook content above. 
If the content doesn't fully answer the question, mention what information is available 
and suggest what additional topics the student might need to explore.
"""
        
        # Generate response
        try:
            response = self.llm_manager.generate_response(
                user_prompt, 
                system_prompt=system_prompt
            )
            return response
        except Exception as e:
            logger.error(f"Error generating RAG response: {e}")
            return "I'm having trouble generating a response right now. Please try again later."
    
    def add_textbook(self, file_path: str, course: str, subject: str, 
                    title: str = None) -> Dict[str, Any]:
        """Add a new textbook to the system"""
        
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                return {"success": False, "error": "File not found"}
            
            if file_path.suffix.lower() != '.pdf':
                return {"success": False, "error": "Only PDF files are supported"}
            
            # Process the PDF
            metadata = {
                "course": course,
                "subject": subject,
                "title": title or file_path.stem,
                "source": str(file_path),
                "type": "textbook"
            }
            
            documents = self.document_processor.process_pdf(str(file_path), metadata)
            
            if not documents:
                return {"success": False, "error": "Failed to process PDF"}
            
            # Add to vector store
            doc_ids = self.vector_store.add_documents(documents)
            
            # Save to database
            db = get_db_session()
            try:
                db_document = DBDocument(
                    title=metadata["title"],
                    file_path=str(file_path),
                    document_type="textbook",
                    course=course,
                    subject=subject,
                    file_size=file_path.stat().st_size,
                    page_count=len(documents),
                    processed=True
                )
                db.add(db_document)
                db.commit()
                
                # Save document chunks
                for i, (doc, doc_id) in enumerate(zip(documents, doc_ids)):
                    chunk = DocumentChunk(
                        document_id=db_document.id,
                        chunk_index=i,
                        content=doc.page_content,
                        page_number=doc.metadata.get("page_number"),
                        vector_id=doc_id,
                        metadata=doc.metadata
                    )
                    db.add(chunk)
                
                db.commit()
                
            finally:
                db.close()
            
            # Save vector store
            self.vector_store.save_index()
            
            logger.info(f"Successfully added textbook: {title} ({len(documents)} chunks)")
            
            return {
                "success": True,
                "message": f"Successfully processed {len(documents)} chunks from the textbook",
                "document_id": db_document.id,
                "chunks_count": len(documents)
            }
            
        except Exception as e:
            logger.error(f"Error adding textbook: {e}")
            return {"success": False, "error": str(e)}
    
    def get_unit_summary(self, course: str, subject: str, unit: str, 
                        language: str = "english") -> Dict[str, Any]:
        """Generate a summary of a specific unit"""
        
        query = f"Summarize unit {unit} of {subject} course {course}"
        
        context = {
            "course": course,
            "subject": subject,
            "language": language
        }
        
        return self.process_query(query, context)
    
    def list_available_textbooks(self) -> List[Dict[str, Any]]:
        """List all available textbooks in the system"""
        
        db = get_db_session()
        try:
            documents = db.query(DBDocument).filter(
                DBDocument.document_type == "textbook",
                DBDocument.processed == True
            ).all()
            
            return [
                {
                    "id": doc.id,
                    "title": doc.title,
                    "course": doc.course,
                    "subject": doc.subject,
                    "page_count": doc.page_count,
                    "created_at": doc.created_at.isoformat()
                }
                for doc in documents
            ]
        finally:
            db.close()
    
    def _get_capabilities(self) -> List[str]:
        """Get list of agent capabilities"""
        return [
            "Answer questions from IGNOU textbooks",
            "Provide unit summaries",
            "Explain concepts with examples",
            "Support multilingual responses (English, Hindi, Tamil)",
            "Process and index new PDF textbooks",
            "Search across multiple textbooks and subjects"
        ]
