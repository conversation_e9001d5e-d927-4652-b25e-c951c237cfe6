# API Keys
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Search APIs
SERPAPI_KEY=your_serpapi_key_here
TAVILY_API_KEY=your_tavily_api_key_here

# Notification Services
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here

# Database
DATABASE_URL=sqlite:///ignou_study_bot.db

# Vector Store Configuration
VECTOR_STORE_TYPE=faiss  # Options: faiss, chroma
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# LLM Configuration
DEFAULT_LLM_PROVIDER=openai  # Options: openai, google, anthropic
DEFAULT_MODEL=gpt-4-turbo-preview
TEMPERATURE=0.7
MAX_TOKENS=2000

# Application Settings
DEBUG=True
LOG_LEVEL=INFO
DATA_DIR=./data
UPLOAD_DIR=./data/uploads

# UI Configuration
STREAMLIT_PORT=8501
FASTAPI_PORT=8000

# Study Configuration
DEFAULT_STUDY_HOURS_PER_DAY=2
DEFAULT_QUIZ_QUESTIONS=10
NOTIFICATION_ENABLED=True
