"""
Vector store implementation for document embeddings and similarity search
"""
import os
import pickle
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import numpy as np

import faiss
from sentence_transformers import SentenceTransformer
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

from config.settings import settings
from utils.logger import logger


class VectorStore:
    """Vector store for document embeddings and similarity search"""
    
    def __init__(self, embedding_model: str = None, vector_store_type: str = None):
        self.embedding_model_name = embedding_model or settings.embedding_model
        self.vector_store_type = vector_store_type or settings.vector_store_type
        
        # Initialize embedding model
        self.embedding_model = SentenceTransformer(self.embedding_model_name)
        self.embedding_dimension = self.embedding_model.get_sentence_embedding_dimension()
        
        # Initialize vector store
        if self.vector_store_type == "faiss":
            self.index = faiss.IndexFlatIP(self.embedding_dimension)  # Inner product for cosine similarity
        else:
            raise ValueError(f"Unsupported vector store type: {self.vector_store_type}")
        
        # Storage for document metadata
        self.documents: List[Dict[str, Any]] = []
        self.document_ids: List[str] = []
        
        # Paths for persistence
        self.index_path = settings.data_path / "vector_store" / "faiss_index.bin"
        self.metadata_path = settings.data_path / "vector_store" / "metadata.pkl"
        
        # Ensure directory exists
        self.index_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Load existing index if available
        self.load_index()
        
        logger.info(f"Vector store initialized with {self.embedding_model_name}")
    
    def add_documents(self, documents: List[Document], document_ids: List[str] = None) -> List[str]:
        """Add documents to the vector store"""
        if document_ids is None:
            document_ids = [f"doc_{len(self.documents) + i}" for i in range(len(documents))]
        
        # Generate embeddings
        texts = [doc.page_content for doc in documents]
        embeddings = self.embedding_model.encode(texts, convert_to_tensor=False)
        
        # Normalize embeddings for cosine similarity
        embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)
        
        # Add to FAISS index
        self.index.add(embeddings.astype('float32'))
        
        # Store metadata
        for i, (doc, doc_id) in enumerate(zip(documents, document_ids)):
            self.documents.append({
                'id': doc_id,
                'content': doc.page_content,
                'metadata': doc.metadata,
                'embedding_index': len(self.document_ids) + i
            })
            self.document_ids.append(doc_id)
        
        logger.info(f"Added {len(documents)} documents to vector store")
        return document_ids
    
    def similarity_search(self, query: str, k: int = 5, score_threshold: float = 0.0) -> List[Dict[str, Any]]:
        """Search for similar documents"""
        if self.index.ntotal == 0:
            logger.warning("Vector store is empty")
            return []
        
        # Generate query embedding
        query_embedding = self.embedding_model.encode([query], convert_to_tensor=False)
        query_embedding = query_embedding / np.linalg.norm(query_embedding, axis=1, keepdims=True)
        
        # Search in FAISS index
        scores, indices = self.index.search(query_embedding.astype('float32'), k)
        
        # Filter by score threshold and prepare results
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if score >= score_threshold and idx < len(self.documents):
                doc = self.documents[idx].copy()
                doc['score'] = float(score)
                results.append(doc)
        
        return results
    
    def get_document_by_id(self, document_id: str) -> Optional[Dict[str, Any]]:
        """Get document by ID"""
        for doc in self.documents:
            if doc['id'] == document_id:
                return doc
        return None
    
    def delete_document(self, document_id: str) -> bool:
        """Delete document by ID (Note: FAISS doesn't support deletion, so we mark as deleted)"""
        for doc in self.documents:
            if doc['id'] == document_id:
                doc['deleted'] = True
                logger.info(f"Marked document {document_id} as deleted")
                return True
        return False
    
    def save_index(self):
        """Save the vector store to disk"""
        try:
            # Save FAISS index
            faiss.write_index(self.index, str(self.index_path))
            
            # Save metadata
            with open(self.metadata_path, 'wb') as f:
                pickle.dump({
                    'documents': self.documents,
                    'document_ids': self.document_ids,
                    'embedding_model_name': self.embedding_model_name
                }, f)
            
            logger.info("Vector store saved successfully")
        except Exception as e:
            logger.error(f"Failed to save vector store: {e}")
    
    def load_index(self):
        """Load the vector store from disk"""
        try:
            if self.index_path.exists() and self.metadata_path.exists():
                # Load FAISS index
                self.index = faiss.read_index(str(self.index_path))
                
                # Load metadata
                with open(self.metadata_path, 'rb') as f:
                    data = pickle.load(f)
                    self.documents = data['documents']
                    self.document_ids = data['document_ids']
                
                logger.info(f"Loaded vector store with {len(self.documents)} documents")
        except Exception as e:
            logger.warning(f"Failed to load existing vector store: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get vector store statistics"""
        active_docs = [doc for doc in self.documents if not doc.get('deleted', False)]
        return {
            'total_documents': len(self.documents),
            'active_documents': len(active_docs),
            'embedding_dimension': self.embedding_dimension,
            'embedding_model': self.embedding_model_name,
            'vector_store_type': self.vector_store_type
        }


class DocumentProcessor:
    """Process documents for vector storage"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
        )
    
    def process_text(self, text: str, metadata: Dict[str, Any] = None) -> List[Document]:
        """Process text into chunks"""
        if metadata is None:
            metadata = {}
        
        chunks = self.text_splitter.split_text(text)
        documents = []
        
        for i, chunk in enumerate(chunks):
            doc_metadata = metadata.copy()
            doc_metadata.update({
                'chunk_index': i,
                'chunk_count': len(chunks)
            })
            
            documents.append(Document(
                page_content=chunk,
                metadata=doc_metadata
            ))
        
        return documents
    
    def process_pdf(self, pdf_path: str, metadata: Dict[str, Any] = None) -> List[Document]:
        """Process PDF file into chunks"""
        try:
            import fitz  # PyMuPDF
            
            doc = fitz.open(pdf_path)
            text = ""
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text += page.get_text()
            
            doc.close()
            
            if metadata is None:
                metadata = {}
            
            metadata.update({
                'source': pdf_path,
                'type': 'pdf',
                'page_count': len(doc)
            })
            
            return self.process_text(text, metadata)
            
        except Exception as e:
            logger.error(f"Failed to process PDF {pdf_path}: {e}")
            return []


# Global vector store instance
vector_store = VectorStore()
document_processor = DocumentProcessor()
