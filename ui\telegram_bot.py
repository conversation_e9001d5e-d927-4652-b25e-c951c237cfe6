"""
Telegram Bot Interface for IGNOU Study Bot
"""
import asyncio
import json
from typing import Dict, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application, CommandHandler, MessageHandler, 
    CallbackQueryHandler, ContextTypes, filters
)

from core.orchestrator import orchestrator
from config.settings import settings
from utils.logger import logger


class IGNOUStudyTelegramBot:
    """Telegram bot for IGNOU Study Bot"""
    
    def __init__(self, token: str):
        self.token = token
        self.application = Application.builder().token(token).build()
        self.setup_handlers()
    
    def setup_handlers(self):
        """Set up command and message handlers"""
        
        # Command handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("menu", self.menu_command))
        self.application.add_handler(<PERSON><PERSON><PERSON><PERSON>("textbook", self.textbook_command))
        self.application.add_handler(CommandHandler("quiz", self.quiz_command))
        self.application.add_handler(<PERSON><PERSON>andler("plan", self.plan_command))
        self.application.add_handler(CommandHandler("progress", self.progress_command))
        
        # Callback query handler for inline keyboards
        self.application.add_handler(CallbackQueryHandler(self.button_callback))
        
        # Message handler for general queries
        self.application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message)
        )
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        user = update.effective_user
        
        welcome_message = f"""
🎓 **Welcome to IGNOU Study Bot, {user.first_name}!**

I'm your AI-powered study assistant with 5 specialized agents to help you succeed in your IGNOU journey:

📚 **Textbook Agent** - Ask questions from your textbooks
📝 **Exam Paper Agent** - Find previous year papers and answers
📅 **Study Planner** - Create personalized study schedules
🧪 **Quiz Master** - Take quizzes and test your knowledge
🧠 **Mentor** - Track progress and get motivation

**Quick Commands:**
/menu - Show main menu
/textbook - Ask textbook questions
/quiz - Take a quiz
/plan - Create study plan
/progress - Check your progress
/help - Get help

**Or just type your question naturally!**

How can I help you study today? 🌟
"""
        
        keyboard = self.get_main_menu_keyboard()
        
        await update.message.reply_text(
            welcome_message,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        
        help_message = """
🆘 **IGNOU Study Bot Help**

**Available Commands:**
• `/start` - Welcome message and main menu
• `/menu` - Show main menu options
• `/textbook` - Ask questions from textbooks
• `/quiz` - Generate and take quizzes
• `/plan` - Create or view study plans
• `/progress` - Check your study progress
• `/help` - Show this help message

**How to Use:**
1. **Ask Questions**: Just type your question naturally
   - "Explain database normalization"
   - "What is object-oriented programming?"

2. **Request Services**: Use natural language
   - "Create a quiz on data structures"
   - "Make a study plan for next month"
   - "Show my progress"

3. **Use Buttons**: Click the menu buttons for quick access

**Tips:**
• Be specific with your questions for better answers
• Mention course/subject for targeted help
• Use the menu for structured interactions

Need more help? Just ask! 😊
"""
        
        await update.message.reply_text(help_message, parse_mode='Markdown')
    
    async def menu_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /menu command"""
        
        menu_message = "🎯 **Main Menu** - Choose what you'd like to do:"
        keyboard = self.get_main_menu_keyboard()
        
        await update.message.reply_text(
            menu_message,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )
    
    def get_main_menu_keyboard(self):
        """Get main menu inline keyboard"""
        
        keyboard = [
            [
                InlineKeyboardButton("📚 Ask Textbook", callback_data="textbook"),
                InlineKeyboardButton("📝 Exam Papers", callback_data="exam_papers")
            ],
            [
                InlineKeyboardButton("📅 Study Plan", callback_data="study_plan"),
                InlineKeyboardButton("🧪 Take Quiz", callback_data="quiz")
            ],
            [
                InlineKeyboardButton("🧠 Check Progress", callback_data="progress"),
                InlineKeyboardButton("💪 Get Motivation", callback_data="motivation")
            ],
            [
                InlineKeyboardButton("ℹ️ Help", callback_data="help")
            ]
        ]
        
        return InlineKeyboardMarkup(keyboard)
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline keyboard button presses"""
        
        query = update.callback_query
        await query.answer()
        
        data = query.data
        
        if data == "textbook":
            await self.handle_textbook_callback(query)
        elif data == "exam_papers":
            await self.handle_exam_papers_callback(query)
        elif data == "study_plan":
            await self.handle_study_plan_callback(query)
        elif data == "quiz":
            await self.handle_quiz_callback(query)
        elif data == "progress":
            await self.handle_progress_callback(query)
        elif data == "motivation":
            await self.handle_motivation_callback(query)
        elif data == "help":
            await query.edit_message_text(
                "Type /help to see all available commands and usage instructions.",
                parse_mode='Markdown'
            )
    
    async def handle_textbook_callback(self, query):
        """Handle textbook button callback"""
        
        message = """
📚 **Textbook Helper**

Ask me any question from your IGNOU textbooks!

**Examples:**
• "Explain the concept of inheritance in OOP"
• "What is database normalization?"
• "Define marketing mix"

**Tips:**
• Be specific with your questions
• Mention the subject if needed
• I can explain in Hindi or Tamil too!

Just type your question below! 👇
"""
        
        await query.edit_message_text(message, parse_mode='Markdown')
    
    async def handle_exam_papers_callback(self, query):
        """Handle exam papers button callback"""
        
        message = """
📝 **Exam Papers Helper**

I can help you with:
• Find previous year question papers
• Generate answers for exam questions
• Predict likely questions
• Exam preparation strategies

**Examples:**
• "Find MCA exam papers for 2023"
• "Answer this question: [your question]"
• "What questions might come in Database exam?"

What would you like help with? 👇
"""
        
        await query.edit_message_text(message, parse_mode='Markdown')
    
    async def handle_study_plan_callback(self, query):
        """Handle study plan button callback"""
        
        message = """
📅 **Study Planner**

Let me help you create a personalized study schedule!

**I can help with:**
• Create daily/weekly study plans
• Break down your syllabus
• Track your progress
• Reschedule missed topics

**To create a plan, tell me:**
• Your subjects/courses
• Available study time per day
• Exam dates (if any)

**Example:**
"Create a study plan for MCA subjects with 2 hours daily study time"

What would you like to plan? 👇
"""
        
        await query.edit_message_text(message, parse_mode='Markdown')
    
    async def handle_quiz_callback(self, query):
        """Handle quiz button callback"""
        
        message = """
🧪 **Quiz Master**

Test your knowledge with AI-generated quizzes!

**I can create:**
• Multiple choice questions
• Subject-specific quizzes
• Different difficulty levels
• Instant scoring and feedback

**Examples:**
• "Create a 10-question quiz on Data Structures"
• "Generate an easy quiz on Marketing"
• "Make a hard quiz on Database Management"

Ready to test your knowledge? Tell me what topic! 👇
"""
        
        await query.edit_message_text(message, parse_mode='Markdown')
    
    async def handle_progress_callback(self, query):
        """Handle progress button callback"""
        
        user_id = str(query.from_user.id)
        
        # Get progress from mentor agent
        response = orchestrator.process_query(
            "Show my progress report",
            user_id=user_id
        )
        
        if response["success"]:
            await query.edit_message_text(
                response["response"],
                parse_mode='Markdown'
            )
        else:
            await query.edit_message_text(
                "Sorry, I couldn't load your progress right now. Please try again later."
            )
    
    async def handle_motivation_callback(self, query):
        """Handle motivation button callback"""
        
        user_id = str(query.from_user.id)
        
        # Get motivation from mentor agent
        response = orchestrator.process_query(
            "I need some motivation to study",
            user_id=user_id
        )
        
        if response["success"]:
            await query.edit_message_text(
                response["response"],
                parse_mode='Markdown'
            )
        else:
            await query.edit_message_text(
                "🌟 You're doing great! Keep up the excellent work! Every step forward is progress. 💪"
            )
    
    async def textbook_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /textbook command"""
        await self.handle_textbook_callback(update.callback_query or update)
    
    async def quiz_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /quiz command"""
        await self.handle_quiz_callback(update.callback_query or update)
    
    async def plan_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /plan command"""
        await self.handle_study_plan_callback(update.callback_query or update)
    
    async def progress_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /progress command"""
        await self.handle_progress_callback(update.callback_query or update)
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle general text messages"""
        
        user_message = update.message.text
        user_id = str(update.effective_user.id)
        
        # Show typing indicator
        await update.message.chat.send_action("typing")
        
        try:
            # Process query through orchestrator
            response = orchestrator.process_query(
                user_message,
                user_id=user_id
            )
            
            if response["success"]:
                # Split long messages
                message_text = response["response"]
                
                if len(message_text) > 4096:  # Telegram message limit
                    # Split into chunks
                    chunks = [message_text[i:i+4096] for i in range(0, len(message_text), 4096)]
                    for chunk in chunks:
                        await update.message.reply_text(chunk, parse_mode='Markdown')
                else:
                    await update.message.reply_text(message_text, parse_mode='Markdown')
                
                # Show menu after response
                if response.get("agent_used") != "mentor":  # Don't show menu after motivation
                    keyboard = self.get_main_menu_keyboard()
                    await update.message.reply_text(
                        "What else can I help you with?",
                        reply_markup=keyboard
                    )
            else:
                await update.message.reply_text(
                    "I'm sorry, I encountered an error processing your request. Please try again or use /help for assistance."
                )
        
        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await update.message.reply_text(
                "I'm experiencing some technical difficulties. Please try again in a moment."
            )
    
    async def run(self):
        """Run the bot"""
        logger.info("Starting Telegram bot...")
        
        # Start the bot
        await self.application.initialize()
        await self.application.start()
        await self.application.updater.start_polling()
        
        logger.info("Telegram bot is running...")
        
        # Keep running
        try:
            await asyncio.Event().wait()
        except KeyboardInterrupt:
            logger.info("Stopping Telegram bot...")
        finally:
            await self.application.updater.stop()
            await self.application.stop()
            await self.application.shutdown()


async def run_bot():
    """Run the Telegram bot"""
    
    if not settings.telegram_bot_token:
        logger.error("Telegram bot token not configured")
        return
    
    bot = IGNOUStudyTelegramBot(settings.telegram_bot_token)
    await bot.run()


if __name__ == "__main__":
    asyncio.run(run_bot())
