# 🎓 IGNOU Study Bot - AI-Powered Multi-Agent Study Assistant

A comprehensive AI-powered system that acts as your exclusive, personalized IGNOU exam study assistant — covering textbooks, past papers, planning, quizzes, and motivation.

## 🏗️ System Architecture: Five Specialized Agents

### 1️⃣ 📚 Textbook Agent (RAG-Based Tutor)
- **Purpose**: Acts as your private IGNOU teacher using official textbooks
- **Features**: Unit summaries, conceptual Q&A, PDF-to-notes conversion, multilingual support
- **Tech**: PDF processing, FAISS vector store, RAG pipeline

### 2️⃣ 📝 Exam Paper Agent (Previous Year Guru)
- **Purpose**: Exam strategy coach using previous year questions
- **Features**: Past paper analysis, answer generation, question prediction
- **Tech**: Web scraping, search APIs, answer generation

### 3️⃣ 📅 Study Planner Agent (Time Manager)
- **Purpose**: Creates personalized daily/weekly study plans
- **Features**: Time-based scheduling, syllabus breakdown, automatic rescheduling
- **Tech**: Python scheduling, SQLite persistence, calendar integration

### 4️⃣ 🧪 Quiz Master Agent (Evaluator)
- **Purpose**: Continuous knowledge testing and evaluation
- **Features**: Quiz generation, instant scoring, weak area identification
- **Tech**: LLM-based quiz creation, performance tracking

### 5️⃣ 🧠 Mentor Agent (Motivator + Tracker)
- **Purpose**: Progress oversight, motivation, and gamification
- **Features**: Progress tracking, motivational prompts, gamification elements
- **Tech**: Streamlit dashboard, notification APIs

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- OpenAI API key or Google AI API key
- Internet connection for web scraping

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd ignou-study-bot

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys
```

### Usage
```bash
# Start the Streamlit web interface
streamlit run app.py

# Or use the command-line interface
python main.py
```

## 📁 Project Structure
```
ignou-study-bot/
├── agents/                 # Individual agent implementations
│   ├── textbook_agent.py
│   ├── exam_paper_agent.py
│   ├── study_planner_agent.py
│   ├── quiz_master_agent.py
│   └── mentor_agent.py
├── core/                   # Shared infrastructure
│   ├── database.py
│   ├── vector_store.py
│   ├── llm_integration.py
│   └── orchestrator.py
├── data/                   # Data storage
│   ├── textbooks/
│   ├── exam_papers/
│   └── user_data/
├── ui/                     # User interfaces
│   ├── streamlit_app.py
│   └── telegram_bot.py
├── tests/                  # Test files
├── config/                 # Configuration files
└── utils/                  # Utility functions
```

## 🔧 Configuration

Create a `.env` file with the following variables:
```
OPENAI_API_KEY=your_openai_api_key
GOOGLE_AI_API_KEY=your_google_ai_api_key
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
SERPAPI_KEY=your_serpapi_key
DATABASE_URL=sqlite:///ignou_study_bot.db
```

## 📚 Features

- **Multi-modal Learning**: Text, PDF, and interactive content
- **Personalized Study Plans**: Adaptive scheduling based on your availability
- **Intelligent Q&A**: Context-aware answers from official textbooks
- **Progress Tracking**: Comprehensive analytics and gamification
- **Multi-language Support**: Tamil, Hindi, and English
- **Exam Preparation**: Previous year papers and predicted questions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support, email <EMAIL> or join our Telegram group.
