"""
Tests for IGNOU Study Bot agents
"""
import pytest
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agents import TextbookAgent, ExamPaperAgent, StudyPlannerAgent, QuizMasterAgent, MentorAgent
from core.orchestrator import orchestrator


class TestTextbookAgent:
    """Test TextbookAgent functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.agent = TextbookAgent()
    
    def test_agent_initialization(self):
        """Test agent initializes correctly"""
        assert self.agent.agent_name == "TextbookAgent"
        assert hasattr(self.agent, 'vector_store')
        assert hasattr(self.agent, 'document_processor')
    
    def test_process_query_validation(self):
        """Test query validation"""
        # Empty query
        response = self.agent.process_query("")
        assert not response["success"]
        
        # Very long query
        long_query = "a" * 20000
        response = self.agent.process_query(long_query)
        assert not response["success"]
    
    def test_process_query_basic(self):
        """Test basic query processing"""
        response = self.agent.process_query("What is database normalization?")
        assert "answer" in response
        assert "success" in response
        assert "timestamp" in response
        assert "agent" in response
    
    def test_get_capabilities(self):
        """Test capabilities listing"""
        capabilities = self.agent._get_capabilities()
        assert isinstance(capabilities, list)
        assert len(capabilities) > 0
        assert any("textbook" in cap.lower() for cap in capabilities)


class TestExamPaperAgent:
    """Test ExamPaperAgent functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.agent = ExamPaperAgent()
    
    def test_agent_initialization(self):
        """Test agent initializes correctly"""
        assert self.agent.agent_name == "ExamPaperAgent"
        assert hasattr(self.agent, 'vector_store')
    
    def test_query_classification(self):
        """Test query classification"""
        # Search papers query
        query_type = self.agent._classify_exam_query("Find previous year papers")
        assert query_type == "search_papers"
        
        # Answer question query
        query_type = self.agent._classify_exam_query("Answer this question")
        assert query_type == "answer_question"
        
        # Predict questions query
        query_type = self.agent._classify_exam_query("What are expected questions")
        assert query_type == "predict_questions"
    
    def test_process_query_basic(self):
        """Test basic query processing"""
        response = self.agent.process_query("Find exam papers for MCA")
        assert "answer" in response
        assert "success" in response


class TestStudyPlannerAgent:
    """Test StudyPlannerAgent functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.agent = StudyPlannerAgent()
    
    def test_agent_initialization(self):
        """Test agent initializes correctly"""
        assert self.agent.agent_name == "StudyPlannerAgent"
        assert hasattr(self.agent, 'default_study_hours')
    
    def test_query_classification(self):
        """Test query classification"""
        # Create plan query
        query_type = self.agent._classify_planning_query("Create a study plan")
        assert query_type == "create_plan"
        
        # Progress query
        query_type = self.agent._classify_planning_query("Check my progress")
        assert query_type == "check_progress"
    
    def test_process_query_basic(self):
        """Test basic query processing"""
        response = self.agent.process_query("Create a study plan for Database Management")
        assert "answer" in response
        assert "success" in response


class TestQuizMasterAgent:
    """Test QuizMasterAgent functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.agent = QuizMasterAgent()
    
    def test_agent_initialization(self):
        """Test agent initializes correctly"""
        assert self.agent.agent_name == "QuizMasterAgent"
        assert hasattr(self.agent, 'vector_store')
        assert hasattr(self.agent, 'default_quiz_length')
    
    def test_query_classification(self):
        """Test query classification"""
        # Generate quiz query
        query_type = self.agent._classify_quiz_query("Generate a quiz")
        assert query_type == "generate_quiz"
        
        # Performance report query
        query_type = self.agent._classify_quiz_query("Show my performance")
        assert query_type == "performance_report"
    
    def test_process_query_basic(self):
        """Test basic query processing"""
        response = self.agent.process_query("Generate a quiz on programming")
        assert "answer" in response
        assert "success" in response


class TestMentorAgent:
    """Test MentorAgent functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.agent = MentorAgent()
    
    def test_agent_initialization(self):
        """Test agent initializes correctly"""
        assert self.agent.agent_name == "MentorAgent"
        assert hasattr(self.agent, 'points_per_task')
        assert hasattr(self.agent, 'points_per_quiz')
    
    def test_query_classification(self):
        """Test query classification"""
        # Progress report query
        query_type = self.agent._classify_mentor_query("Show my progress")
        assert query_type == "progress_report"
        
        # Motivation query
        query_type = self.agent._classify_mentor_query("I need motivation")
        assert query_type == "motivation"
    
    def test_process_query_basic(self):
        """Test basic query processing"""
        response = self.agent.process_query("I need some motivation", user_id="test_user")
        assert "answer" in response
        assert "success" in response


class TestOrchestrator:
    """Test orchestrator functionality"""
    
    def test_orchestrator_initialization(self):
        """Test orchestrator initializes correctly"""
        assert hasattr(orchestrator, 'intent_classifier')
        assert hasattr(orchestrator, 'workflow')
    
    def test_intent_classification(self):
        """Test intent classification"""
        # Textbook query
        intent = orchestrator.intent_classifier.classify_intent("Explain database normalization")
        assert intent.value == "textbook"
        
        # Quiz query
        intent = orchestrator.intent_classifier.classify_intent("Create a quiz")
        assert intent.value == "quiz_master"
        
        # Study plan query
        intent = orchestrator.intent_classifier.classify_intent("Make a study plan")
        assert intent.value == "study_planner"
    
    def test_process_query_basic(self):
        """Test basic query processing through orchestrator"""
        response = orchestrator.process_query("What is object-oriented programming?")
        assert "response" in response
        assert "success" in response
        assert "intent" in response
        assert "agent_used" in response


# Integration tests
class TestIntegration:
    """Integration tests for the complete system"""
    
    def test_end_to_end_textbook_query(self):
        """Test end-to-end textbook query"""
        query = "What is database normalization?"
        response = orchestrator.process_query(query, user_id="test_user")
        
        assert response["success"] is True
        assert response["intent"] == "textbook"
        assert response["agent_used"] == "textbook"
        assert len(response["response"]) > 0
    
    def test_end_to_end_quiz_query(self):
        """Test end-to-end quiz query"""
        query = "Generate a quiz on programming"
        response = orchestrator.process_query(query, user_id="test_user")
        
        assert response["success"] is True
        assert response["intent"] == "quiz_master"
        assert response["agent_used"] == "quiz_master"
        assert len(response["response"]) > 0
    
    def test_end_to_end_motivation_query(self):
        """Test end-to-end motivation query"""
        query = "I need motivation to study"
        response = orchestrator.process_query(query, user_id="test_user")
        
        assert response["success"] is True
        assert response["intent"] == "mentor"
        assert response["agent_used"] == "mentor"
        assert len(response["response"]) > 0


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
