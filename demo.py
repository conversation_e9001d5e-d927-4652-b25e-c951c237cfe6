"""
Demo script for IGNOU Study Bot
Demonstrates the capabilities of all five agents
"""
import asyncio
from datetime import datetime

from core.orchestrator import orchestrator
from agents import TextbookAgent, ExamPaperAgent, StudyPlannerAgent, QuizMasterAgent, MentorAgent
from utils.logger import logger


def print_separator(title: str):
    """Print a formatted separator"""
    print("\n" + "="*60)
    print(f"🎯 {title}")
    print("="*60)


def print_response(response: dict):
    """Print agent response in a formatted way"""
    if response.get("success"):
        print("✅ Success!")
        print(f"📝 Response: {response['answer'][:200]}...")
        if "metadata" in response:
            print(f"ℹ️  Metadata: {response['metadata']}")
    else:
        print("❌ Error!")
        print(f"🚨 Error: {response.get('error', 'Unknown error')}")
    print("-" * 40)


async def demo_orchestrator():
    """Demonstrate the orchestrator with different query types"""
    print_separator("ORCHESTRATOR DEMO - Multi-Agent Coordination")
    
    demo_queries = [
        ("Textbook Query", "What is database normalization?"),
        ("Quiz Query", "Create a 5-question quiz on programming"),
        ("Study Plan Query", "Create a study plan for MCA subjects"),
        ("Exam Paper Query", "Find previous year papers for Database Management"),
        ("Motivation Query", "I need motivation to study today")
    ]
    
    for query_type, query in demo_queries:
        print(f"\n🔍 {query_type}: '{query}'")
        
        response = orchestrator.process_query(query, user_id="demo_user")
        
        print(f"🎯 Intent: {response.get('intent', 'Unknown')}")
        print(f"🤖 Agent Used: {response.get('agent_used', 'Unknown')}")
        print(f"✅ Success: {response.get('success', False)}")
        print(f"📝 Response Preview: {response.get('response', '')[:150]}...")
        
        if response.get('metadata'):
            print(f"ℹ️  Metadata Keys: {list(response['metadata'].keys())}")
        
        print("-" * 40)


def demo_textbook_agent():
    """Demonstrate TextbookAgent capabilities"""
    print_separator("TEXTBOOK AGENT DEMO - RAG-Based Tutor")
    
    agent = TextbookAgent()
    
    # Show capabilities
    print("🎯 Agent Capabilities:")
    for capability in agent._get_capabilities():
        print(f"  • {capability}")
    
    # Demo queries
    demo_queries = [
        "What is object-oriented programming?",
        "Explain database normalization with examples",
        "Define the concept of inheritance in OOP"
    ]
    
    for query in demo_queries:
        print(f"\n❓ Query: {query}")
        response = agent.process_query(query, user_id="demo_user")
        print_response(response)
    
    # Demo with context
    print("\n🔧 Query with Context:")
    context = {"course": "MCA", "subject": "Database Management", "language": "english"}
    response = agent.process_query("Explain ACID properties", context, "demo_user")
    print_response(response)


def demo_exam_paper_agent():
    """Demonstrate ExamPaperAgent capabilities"""
    print_separator("EXAM PAPER AGENT - Previous Year Guru")
    
    agent = ExamPaperAgent()
    
    print("🎯 Agent Capabilities:")
    for capability in agent._get_capabilities():
        print(f"  • {capability}")
    
    demo_queries = [
        "Find exam papers for MCA Database Management",
        "Answer this question: What are the advantages of DBMS?",
        "What questions might come in my programming exam?",
        "Give me exam preparation tips for IGNOU"
    ]
    
    for query in demo_queries:
        print(f"\n❓ Query: {query}")
        response = agent.process_query(query, user_id="demo_user")
        print_response(response)


def demo_study_planner_agent():
    """Demonstrate StudyPlannerAgent capabilities"""
    print_separator("STUDY PLANNER AGENT - Time Manager")
    
    agent = StudyPlannerAgent()
    
    print("🎯 Agent Capabilities:")
    for capability in agent._get_capabilities():
        print(f"  • {capability}")
    
    demo_queries = [
        "Create a study plan for Database Management and Programming",
        "What should I study today?",
        "Check my study progress",
        "I have 2 hours daily, plan my studies for next month"
    ]
    
    for query in demo_queries:
        print(f"\n❓ Query: {query}")
        context = {"subjects": ["Database Management", "Programming"], "daily_hours": 2}
        response = agent.process_query(query, context, "demo_user")
        print_response(response)


def demo_quiz_master_agent():
    """Demonstrate QuizMasterAgent capabilities"""
    print_separator("QUIZ MASTER AGENT - Knowledge Evaluator")
    
    agent = QuizMasterAgent()
    
    print("🎯 Agent Capabilities:")
    for capability in agent._get_capabilities():
        print(f"  • {capability}")
    
    demo_queries = [
        "Generate a 5-question quiz on programming concepts",
        "Create an easy quiz on database basics",
        "Show my quiz performance report"
    ]
    
    for query in demo_queries:
        print(f"\n❓ Query: {query}")
        context = {"topic": "programming", "num_questions": 5, "difficulty": "medium"}
        response = agent.process_query(query, context, "demo_user")
        print_response(response)


def demo_mentor_agent():
    """Demonstrate MentorAgent capabilities"""
    print_separator("MENTOR AGENT - Motivational Coach")
    
    agent = MentorAgent()
    
    print("🎯 Agent Capabilities:")
    for capability in agent._get_capabilities():
        print(f"  • {capability}")
    
    demo_queries = [
        "Show my progress report",
        "I'm feeling unmotivated today, help me",
        "Show my achievements and badges",
        "I completed my assignment today!"
    ]
    
    for query in demo_queries:
        print(f"\n❓ Query: {query}")
        response = agent.process_query(query, user_id="demo_user")
        print_response(response)


def demo_system_integration():
    """Demonstrate system integration and workflow"""
    print_separator("SYSTEM INTEGRATION DEMO - Complete Workflow")
    
    print("🔄 Simulating a complete study session workflow:")
    
    # Step 1: Check today's plan
    print("\n1️⃣ Checking today's study plan...")
    response = orchestrator.process_query("What should I study today?", user_id="demo_user")
    print(f"📅 Plan: {response['response'][:100]}...")
    
    # Step 2: Ask a textbook question
    print("\n2️⃣ Learning from textbook...")
    response = orchestrator.process_query("Explain database normalization", user_id="demo_user")
    print(f"📚 Learning: {response['response'][:100]}...")
    
    # Step 3: Take a quiz
    print("\n3️⃣ Testing knowledge with quiz...")
    response = orchestrator.process_query("Create a 3-question quiz on databases", user_id="demo_user")
    print(f"🧪 Quiz: {response['response'][:100]}...")
    
    # Step 4: Check progress
    print("\n4️⃣ Checking progress...")
    response = orchestrator.process_query("Show my progress", user_id="demo_user")
    print(f"📊 Progress: {response['response'][:100]}...")
    
    # Step 5: Get motivation
    print("\n5️⃣ Getting motivation...")
    response = orchestrator.process_query("Great job! I completed today's study session", user_id="demo_user")
    print(f"💪 Motivation: {response['response'][:100]}...")


def main():
    """Run the complete demo"""
    print("🎓 IGNOU STUDY BOT - COMPREHENSIVE DEMO")
    print("=" * 60)
    print("This demo showcases all five AI agents and their capabilities")
    print(f"Demo started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Demo individual agents
        demo_textbook_agent()
        demo_exam_paper_agent()
        demo_study_planner_agent()
        demo_quiz_master_agent()
        demo_mentor_agent()
        
        # Demo orchestrator
        asyncio.run(demo_orchestrator())
        
        # Demo system integration
        demo_system_integration()
        
        print_separator("DEMO COMPLETED SUCCESSFULLY! 🎉")
        print("✅ All agents are working correctly")
        print("✅ Orchestrator is routing queries properly")
        print("✅ System integration is functional")
        print("\n🚀 Your IGNOU Study Bot is ready to use!")
        print("\nNext steps:")
        print("1. Upload your textbook PDFs")
        print("2. Create your first study plan")
        print("3. Start your learning journey!")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        logger.error(f"Demo error: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Check your API keys in .env file")
        print("2. Ensure all dependencies are installed")
        print("3. Run: python main.py --setup")


if __name__ == "__main__":
    main()
