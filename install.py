"""
Installation script for IGNOU Study Bot
Handles dependency installation and setup
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ Python 3.9 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    # First install pydantic-settings specifically
    if not run_command("pip install pydantic-settings==2.1.0", "Installing pydantic-settings"):
        return False
    
    # Then install all requirements
    if not run_command("pip install -r requirements.txt", "Installing all requirements"):
        return False
    
    return True


def create_env_file():
    """Create .env file from template if it doesn't exist"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("📝 Creating .env file from template...")
        try:
            with open(env_example, 'r') as src, open(env_file, 'w') as dst:
                dst.write(src.read())
            print("✅ .env file created successfully")
            print("⚠️  Please edit .env file and add your API keys")
            return True
        except Exception as e:
            print(f"❌ Failed to create .env file: {e}")
            return False
    elif env_file.exists():
        print("✅ .env file already exists")
        return True
    else:
        print("⚠️  .env.example not found, please create .env manually")
        return False


def setup_database():
    """Initialize the database"""
    print("🗄️ Setting up database...")
    try:
        # Import here to avoid import errors before dependencies are installed
        from core.database import init_database
        init_database()
        print("✅ Database initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False


def main():
    """Main installation process"""
    print("🎓 IGNOU Study Bot - Installation Script")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Installation failed at dependency installation")
        print("💡 Try running manually: pip install pydantic-settings==2.1.0")
        print("💡 Then run: pip install -r requirements.txt")
        sys.exit(1)
    
    # Create .env file
    create_env_file()
    
    # Setup database
    if not setup_database():
        print("\n⚠️  Database setup failed, but you can try later with:")
        print("python main.py --setup")
    
    print("\n🎉 Installation completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file and add your API keys")
    print("2. Run: streamlit run app.py")
    print("3. Or run: python demo.py to test the system")
    print("\n📚 Documentation:")
    print("- INSTALLATION.md - Detailed setup guide")
    print("- USAGE_GUIDE.md - How to use the system")
    print("- README.md - Project overview")


if __name__ == "__main__":
    main()
