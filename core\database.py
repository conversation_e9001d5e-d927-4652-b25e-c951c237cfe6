"""
Database models and operations for IGNOU Study Bot
"""
from datetime import datetime
from typing import Optional, List
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Float, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, Session
from sqlalchemy.dialects.sqlite import JSON

from config.settings import settings
from utils.logger import logger

Base = declarative_base()


class User(Base):
    """User model for storing user information"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    full_name = Column(String(100))
    course = Column(String(50))  # IGNOU course code
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    study_plans = relationship("StudyPlan", back_populates="user")
    quiz_attempts = relationship("QuizAttempt", back_populates="user")
    progress_records = relationship("ProgressRecord", back_populates="user")


class StudyPlan(Base):
    """Study plan model for storing user study schedules"""
    __tablename__ = "study_plans"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    title = Column(String(200))
    description = Column(Text)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    daily_hours = Column(Float, default=2.0)
    subjects = Column(JSON)  # List of subjects/units
    status = Column(String(20), default="active")  # active, completed, paused
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="study_plans")
    tasks = relationship("StudyTask", back_populates="study_plan")


class StudyTask(Base):
    """Individual study tasks within a study plan"""
    __tablename__ = "study_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    study_plan_id = Column(Integer, ForeignKey("study_plans.id"))
    title = Column(String(200))
    description = Column(Text)
    subject = Column(String(100))
    unit = Column(String(100))
    scheduled_date = Column(DateTime)
    completed_date = Column(DateTime, nullable=True)
    status = Column(String(20), default="pending")  # pending, in_progress, completed, skipped
    estimated_hours = Column(Float, default=1.0)
    actual_hours = Column(Float, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    study_plan = relationship("StudyPlan", back_populates="tasks")


class Document(Base):
    """Document model for storing textbooks and exam papers"""
    __tablename__ = "documents"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200))
    file_path = Column(String(500))
    document_type = Column(String(50))  # textbook, exam_paper, notes
    course = Column(String(50))
    subject = Column(String(100))
    year = Column(Integer, nullable=True)
    file_size = Column(Integer)
    page_count = Column(Integer, nullable=True)
    processed = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    chunks = relationship("DocumentChunk", back_populates="document")


class DocumentChunk(Base):
    """Document chunks for vector storage"""
    __tablename__ = "document_chunks"
    
    id = Column(Integer, primary_key=True, index=True)
    document_id = Column(Integer, ForeignKey("documents.id"))
    chunk_index = Column(Integer)
    content = Column(Text)
    page_number = Column(Integer, nullable=True)
    vector_id = Column(String(100), nullable=True)  # ID in vector store
    metadata = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    document = relationship("Document", back_populates="chunks")


class Quiz(Base):
    """Quiz model for storing generated quizzes"""
    __tablename__ = "quizzes"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200))
    subject = Column(String(100))
    unit = Column(String(100))
    difficulty = Column(String(20), default="medium")  # easy, medium, hard
    questions = Column(JSON)  # List of questions with options and answers
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    attempts = relationship("QuizAttempt", back_populates="quiz")


class QuizAttempt(Base):
    """Quiz attempt model for tracking user quiz performance"""
    __tablename__ = "quiz_attempts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    quiz_id = Column(Integer, ForeignKey("quizzes.id"))
    answers = Column(JSON)  # User's answers
    score = Column(Float)
    total_questions = Column(Integer)
    time_taken = Column(Integer)  # in seconds
    completed_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="quiz_attempts")
    quiz = relationship("Quiz", back_populates="attempts")


class ProgressRecord(Base):
    """Progress tracking model"""
    __tablename__ = "progress_records"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    subject = Column(String(100))
    unit = Column(String(100))
    progress_type = Column(String(50))  # study, quiz, revision
    progress_value = Column(Float)  # percentage or score
    date = Column(DateTime, default=datetime.utcnow)
    metadata = Column(JSON)
    
    # Relationships
    user = relationship("User", back_populates="progress_records")


# Database engine and session
engine = create_engine(settings.database_url, echo=settings.debug)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def init_database():
    """Initialize the database by creating all tables"""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise


def get_db() -> Session:
    """Get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_db_session() -> Session:
    """Get database session for direct use"""
    return SessionLocal()
