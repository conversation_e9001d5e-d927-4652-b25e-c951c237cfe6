# 🚀 IGNOU Study Bot - Installation Guide

This guide will help you set up the IGNOU Study Bot on your system.

## 📋 Prerequisites

- **Python 3.9+** (recommended: Python 3.10 or 3.11)
- **Git** (for cloning the repository)
- **Internet connection** (for downloading dependencies and API access)
- **API Keys** (at least one of: OpenAI, Google AI, or Anthropic)

## 🛠️ Installation Steps

### 1. Clone the Repository

```bash
git clone https://github.com/ignoustudybot/ignou-study-bot.git
cd ignou-study-bot
```

### 2. Create Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate

# On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies

```bash
# Install all required packages
pip install -r requirements.txt

# Or install in development mode
pip install -e .
```

### 4. Set Up Environment Variables

```bash
# Copy the example environment file
cp .env.example .env

# Edit the .env file with your API keys
# Use your preferred text editor
notepad .env  # Windows
nano .env     # Linux/macOS
```

**Required API Keys (at least one):**
- `OPENAI_API_KEY` - Get from [OpenAI](https://platform.openai.com/api-keys)
- `GOOGLE_AI_API_KEY` - Get from [Google AI Studio](https://makersuite.google.com/app/apikey)
- `ANTHROPIC_API_KEY` - Get from [Anthropic](https://console.anthropic.com/)

**Optional API Keys:**
- `SERPAPI_KEY` - For web search (get from [SerpAPI](https://serpapi.com/))
- `TAVILY_API_KEY` - Alternative web search (get from [Tavily](https://tavily.com/))
- `TELEGRAM_BOT_TOKEN` - For Telegram bot (get from [@BotFather](https://t.me/botfather))

### 5. Initialize the Database

```bash
# Run the setup command
python main.py --setup
```

This will:
- Create required directories
- Initialize the SQLite database
- Check API key configuration
- Set up logging

### 6. Test the Installation

```bash
# Run basic tests
python -m pytest tests/ -v

# Or test individual components
python -c "from core.orchestrator import orchestrator; print('✅ Orchestrator loaded successfully')"
python -c "from agents import TextbookAgent; print('✅ Agents loaded successfully')"
```

## 🎯 Running the Application

### Option 1: Streamlit Web Interface (Recommended)

```bash
# Start the web interface
streamlit run app.py

# Or use the main script
python main.py --mode web
```

The web interface will be available at: `http://localhost:8501`

### Option 2: Command Line Interface

```bash
# Start CLI mode
python main.py --mode cli
```

### Option 3: Telegram Bot

```bash
# Make sure TELEGRAM_BOT_TOKEN is set in .env
python main.py --mode telegram
```

## 📚 Adding Your First Textbook

1. **Prepare PDF Files**: Ensure your IGNOU textbooks are in PDF format
2. **Use the Web Interface**: 
   - Go to "📚 Textbook Helper" page
   - Use the "📤 Upload Textbook" section
   - Provide course code and subject name
   - Upload and process the PDF

3. **Or use Python directly**:
```python
from agents import TextbookAgent

agent = TextbookAgent()
result = agent.add_textbook(
    file_path="path/to/your/textbook.pdf",
    course="MCA",
    subject="Database Management Systems",
    title="Database Management Systems - Block 1"
)
print(result)
```

## 🔧 Configuration Options

### Database Configuration
- Default: SQLite database (`ignou_study_bot.db`)
- For production: Consider PostgreSQL or MySQL
- Update `DATABASE_URL` in `.env` file

### LLM Configuration
- Default provider: OpenAI GPT-4
- Alternative: Google Gemini Pro
- Configure in `.env`: `DEFAULT_LLM_PROVIDER=openai`

### Vector Store Configuration
- Default: FAISS (local)
- Alternative: ChromaDB
- Configure in `.env`: `VECTOR_STORE_TYPE=faiss`

## 🐛 Troubleshooting

### Common Issues

**1. Import Errors**
```bash
# Make sure you're in the virtual environment
source venv/bin/activate  # or venv\Scripts\activate on Windows

# Reinstall dependencies
pip install -r requirements.txt
```

**2. API Key Issues**
```bash
# Check if API keys are loaded
python -c "from config.settings import settings; print(f'OpenAI: {bool(settings.openai_api_key)}')"
```

**3. Database Issues**
```bash
# Reset database
rm ignou_study_bot.db
python main.py --setup
```

**4. Port Already in Use (Streamlit)**
```bash
# Use different port
streamlit run app.py --server.port 8502
```

**5. Memory Issues with Large PDFs**
- Process PDFs in smaller chunks
- Increase system memory
- Use cloud deployment for large-scale usage

### Getting Help

1. **Check Logs**: Look in the `logs/` directory for error details
2. **Run Tests**: `python -m pytest tests/ -v` to identify issues
3. **GitHub Issues**: Report bugs at the project repository
4. **Documentation**: Check the README.md for additional information

## 🚀 Next Steps

1. **Upload Textbooks**: Add your IGNOU course materials
2. **Create Study Plan**: Use the Study Planner agent
3. **Take Quizzes**: Test your knowledge with the Quiz Master
4. **Track Progress**: Monitor your learning with the Mentor agent
5. **Explore Features**: Try all five AI agents

## 📈 Performance Optimization

### For Better Performance:
1. **Use SSD storage** for faster database operations
2. **Allocate sufficient RAM** (minimum 4GB recommended)
3. **Use GPU** if available for faster embeddings
4. **Consider cloud deployment** for multiple users

### Production Deployment:
1. **Use PostgreSQL** instead of SQLite
2. **Set up Redis** for caching
3. **Use Docker** for containerization
4. **Configure load balancing** for high traffic

## 🔒 Security Considerations

1. **Keep API keys secure** - never commit them to version control
2. **Use environment variables** for sensitive configuration
3. **Regularly update dependencies** to patch security vulnerabilities
4. **Implement user authentication** for production use
5. **Use HTTPS** in production deployments

---

**Need help?** Check our [FAQ](FAQ.md) or create an issue on GitHub!
