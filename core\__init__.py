"""Core infrastructure for IGNOU Study Bot"""

from .database import (
    init_database, get_db, get_db_session,
    User, StudyPlan, StudyTask, Document, DocumentChunk,
    Quiz, QuizAttempt, ProgressRecord
)
from .vector_store import vector_store, document_processor, VectorStore, DocumentProcessor
from .llm_integration import llm_manager, PromptTemplates, LLMManager

__all__ = [
    # Database
    "init_database", "get_db", "get_db_session",
    "User", "StudyPlan", "StudyTask", "Document", "DocumentChunk",
    "Quiz", "QuizAttempt", "ProgressRecord",
    
    # Vector Store
    "vector_store", "document_processor", "VectorStore", "DocumentProcessor",
    
    # LLM Integration
    "llm_manager", "PromptTemplates", "LLMManager"
]
