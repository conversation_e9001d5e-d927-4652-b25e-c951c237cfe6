"""
Configuration settings for IGNOU Study Bot
"""
import os
from pathlib import Path
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables"""
    
    # API Keys
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    google_ai_api_key: Optional[str] = Field(None, env="GOOGLE_AI_API_KEY")
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    
    # Search APIs
    serpapi_key: Optional[str] = Field(None, env="SERPAPI_KEY")
    tavily_api_key: Optional[str] = Field(None, env="TAVILY_API_KEY")
    
    # Notification Services
    telegram_bot_token: Optional[str] = Field(None, env="TELEGRAM_BOT_TOKEN")
    twilio_account_sid: Optional[str] = Field(None, env="TWILIO_ACCOUNT_SID")
    twilio_auth_token: Optional[str] = Field(None, env="TWILIO_AUTH_TOKEN")
    
    # Database
    database_url: str = Field("sqlite:///ignou_study_bot.db", env="DATABASE_URL")
    
    # Vector Store Configuration
    vector_store_type: str = Field("faiss", env="VECTOR_STORE_TYPE")
    embedding_model: str = Field("sentence-transformers/all-MiniLM-L6-v2", env="EMBEDDING_MODEL")
    
    # LLM Configuration
    default_llm_provider: str = Field("openai", env="DEFAULT_LLM_PROVIDER")
    default_model: str = Field("gpt-4-turbo-preview", env="DEFAULT_MODEL")
    temperature: float = Field(0.7, env="TEMPERATURE")
    max_tokens: int = Field(2000, env="MAX_TOKENS")
    
    # Application Settings
    debug: bool = Field(True, env="DEBUG")
    log_level: str = Field("INFO", env="LOG_LEVEL")
    data_dir: str = Field("./data", env="DATA_DIR")
    upload_dir: str = Field("./data/uploads", env="UPLOAD_DIR")
    
    # UI Configuration
    streamlit_port: int = Field(8501, env="STREAMLIT_PORT")
    fastapi_port: int = Field(8000, env="FASTAPI_PORT")
    
    # Study Configuration
    default_study_hours_per_day: int = Field(2, env="DEFAULT_STUDY_HOURS_PER_DAY")
    default_quiz_questions: int = Field(10, env="DEFAULT_QUIZ_QUESTIONS")
    notification_enabled: bool = Field(True, env="NOTIFICATION_ENABLED")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    @property
    def project_root(self) -> Path:
        """Get the project root directory"""
        return Path(__file__).parent.parent
    
    @property
    def data_path(self) -> Path:
        """Get the data directory path"""
        return self.project_root / self.data_dir
    
    @property
    def textbooks_path(self) -> Path:
        """Get the textbooks directory path"""
        return self.data_path / "textbooks"
    
    @property
    def exam_papers_path(self) -> Path:
        """Get the exam papers directory path"""
        return self.data_path / "exam_papers"
    
    @property
    def user_data_path(self) -> Path:
        """Get the user data directory path"""
        return self.data_path / "user_data"
    
    def ensure_directories(self):
        """Ensure all required directories exist"""
        directories = [
            self.data_path,
            self.textbooks_path,
            self.exam_papers_path,
            self.user_data_path,
            self.project_root / self.upload_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)


# Global settings instance
settings = Settings()

# Ensure directories exist on import
settings.ensure_directories()
