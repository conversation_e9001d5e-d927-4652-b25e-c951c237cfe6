"""
Study Planner Agent - Time Manager for IGNOU Study <PERSON><PERSON>
Handles study planning, scheduling, and progress tracking
"""
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta, date
from dataclasses import dataclass
import schedule

from agents.base_agent import BaseAgent, AgentResponse
from core.database import get_db_session, User, StudyPlan, StudyTask
from core.llm_integration import PromptTemplates
from config.settings import settings
from utils.logger import logger


@dataclass
class StudySession:
    """Represents a single study session"""
    subject: str
    unit: str
    duration_hours: float
    date: date
    priority: int = 1  # 1=high, 2=medium, 3=low
    completed: bool = False


class StudyPlannerAgent(BaseAgent):
    """
    Study Planner Agent that creates and manages personalized study schedules.
    
    Capabilities:
    - Create personalized daily/weekly study plans
    - Break down syllabus into manageable chunks
    - Track study progress and completion
    - Automatically reschedule missed topics
    - Provide study reminders and notifications
    - Optimize study schedule based on exam dates
    """
    
    def __init__(self):
        super().__init__("StudyPlannerAgent")
        self.default_study_hours = settings.default_study_hours_per_day
    
    def process_query(self, query: str, context: Dict[str, Any] = None, 
                     user_id: str = None) -> Dict[str, Any]:
        """Process study planning related queries"""
        
        if not self._validate_input(query):
            return self._format_error_response("Invalid query provided")
        
        try:
            context = context or {}
            
            # Determine query type
            query_type = self._classify_planning_query(query)
            
            if query_type == "create_plan":
                return self._create_study_plan(query, context, user_id)
            elif query_type == "update_plan":
                return self._update_study_plan(query, context, user_id)
            elif query_type == "check_progress":
                return self._check_progress(user_id)
            elif query_type == "reschedule":
                return self._reschedule_tasks(query, context, user_id)
            elif query_type == "daily_schedule":
                return self._get_daily_schedule(context, user_id)
            else:
                return self._general_planning_help(query, context)
                
        except Exception as e:
            logger.error(f"Error in study planner agent: {e}")
            return self._format_error_response(str(e))
    
    def _classify_planning_query(self, query: str) -> str:
        """Classify the type of planning query"""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ["create", "make", "new plan", "plan for"]):
            return "create_plan"
        elif any(word in query_lower for word in ["update", "modify", "change", "reschedule"]):
            return "update_plan"
        elif any(word in query_lower for word in ["progress", "completed", "status", "track"]):
            return "check_progress"
        elif any(word in query_lower for word in ["today", "tomorrow", "daily", "schedule"]):
            return "daily_schedule"
        elif "reschedule" in query_lower:
            return "reschedule"
        else:
            return "general"
    
    def _create_study_plan(self, query: str, context: Dict[str, Any], 
                          user_id: str = None) -> Dict[str, Any]:
        """Create a new study plan"""
        
        try:
            # Extract planning parameters from query and context
            plan_params = self._extract_planning_parameters(query, context)
            
            if not plan_params.get("subjects"):
                return self._format_success_response(
                    "To create a study plan, I need more information:\n\n"
                    "1. **Subjects/Courses**: What subjects do you want to study?\n"
                    "2. **Available Time**: How many hours per day can you study?\n"
                    "3. **Exam Dates**: When are your exams?\n"
                    "4. **Priority Topics**: Any specific topics you want to focus on?\n\n"
                    "Please provide these details so I can create a personalized study plan for you."
                )
            
            # Generate study plan
            study_plan = self._generate_study_plan(plan_params, user_id)
            
            # Save to database
            plan_id = self._save_study_plan(study_plan, user_id)
            
            # Format response
            response = self._format_study_plan_response(study_plan)
            
            metadata = {
                "plan_id": plan_id,
                "total_subjects": len(plan_params["subjects"]),
                "daily_hours": plan_params.get("daily_hours", self.default_study_hours),
                "duration_days": plan_params.get("duration_days", 30)
            }
            
            return self._format_success_response(response, metadata)
            
        except Exception as e:
            logger.error(f"Error creating study plan: {e}")
            return self._format_error_response("Failed to create study plan")
    
    def _extract_planning_parameters(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract planning parameters from query and context"""
        
        # Use LLM to extract structured information from the query
        extraction_prompt = f"""
        Extract study planning parameters from the following query. Return a JSON object with these fields:
        - subjects: list of subjects/courses mentioned
        - daily_hours: number of study hours per day (default: 2)
        - duration_days: total planning duration in days (default: 30)
        - exam_dates: any exam dates mentioned
        - priority_topics: any specific topics mentioned as priority
        - start_date: when to start (default: today)
        
        Query: {query}
        Context: {json.dumps(context)}
        
        Return only valid JSON:
        """
        
        try:
            response = self.llm_manager.generate_response(extraction_prompt)
            # Try to extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                params = json.loads(json_match.group())
                return params
        except Exception as e:
            logger.warning(f"Failed to extract parameters with LLM: {e}")
        
        # Fallback to context-based extraction
        return {
            "subjects": context.get("subjects", []),
            "daily_hours": context.get("daily_hours", self.default_study_hours),
            "duration_days": context.get("duration_days", 30),
            "exam_dates": context.get("exam_dates", []),
            "priority_topics": context.get("priority_topics", []),
            "start_date": context.get("start_date", datetime.now().date())
        }
    
    def _generate_study_plan(self, params: Dict[str, Any], user_id: str = None) -> Dict[str, Any]:
        """Generate a detailed study plan"""
        
        subjects = params.get("subjects", [])
        daily_hours = params.get("daily_hours", self.default_study_hours)
        duration_days = params.get("duration_days", 30)
        start_date = params.get("start_date", datetime.now().date())
        
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
        
        # Calculate total available study hours
        total_hours = daily_hours * duration_days
        
        # Distribute hours among subjects (equal distribution for now)
        hours_per_subject = total_hours / len(subjects) if subjects else 0
        
        # Generate daily schedule
        daily_schedule = []
        current_date = start_date
        subject_index = 0
        
        for day in range(duration_days):
            if subjects:
                current_subject = subjects[subject_index % len(subjects)]
                
                # Create study session for the day
                session = StudySession(
                    subject=current_subject,
                    unit=f"Unit {(day // len(subjects)) + 1}",
                    duration_hours=daily_hours,
                    date=current_date,
                    priority=1 if day < duration_days * 0.7 else 2  # Higher priority for first 70% of time
                )
                
                daily_schedule.append(session)
                subject_index += 1
            
            current_date += timedelta(days=1)
        
        return {
            "title": f"Study Plan - {', '.join(subjects[:2])}{'...' if len(subjects) > 2 else ''}",
            "subjects": subjects,
            "daily_hours": daily_hours,
            "duration_days": duration_days,
            "start_date": start_date,
            "end_date": start_date + timedelta(days=duration_days-1),
            "total_hours": total_hours,
            "daily_schedule": daily_schedule,
            "parameters": params
        }
    
    def _save_study_plan(self, study_plan: Dict[str, Any], user_id: str = None) -> int:
        """Save study plan to database"""
        
        db = get_db_session()
        try:
            # Create study plan record
            db_plan = StudyPlan(
                user_id=user_id,
                title=study_plan["title"],
                description=f"Study plan for {', '.join(study_plan['subjects'])}",
                start_date=datetime.combine(study_plan["start_date"], datetime.min.time()),
                end_date=datetime.combine(study_plan["end_date"], datetime.min.time()),
                daily_hours=study_plan["daily_hours"],
                subjects=study_plan["subjects"],
                status="active"
            )
            db.add(db_plan)
            db.flush()  # Get the ID
            
            # Create study tasks
            for session in study_plan["daily_schedule"]:
                task = StudyTask(
                    study_plan_id=db_plan.id,
                    title=f"Study {session.subject} - {session.unit}",
                    description=f"Study session for {session.subject}",
                    subject=session.subject,
                    unit=session.unit,
                    scheduled_date=datetime.combine(session.date, datetime.min.time()),
                    estimated_hours=session.duration_hours,
                    status="pending"
                )
                db.add(task)
            
            db.commit()
            return db_plan.id
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error saving study plan: {e}")
            raise
        finally:
            db.close()
    
    def _format_study_plan_response(self, study_plan: Dict[str, Any]) -> str:
        """Format study plan as a readable response"""
        
        response = f"""
**📅 Your Personalized Study Plan**

**Plan Overview:**
- **Duration**: {study_plan['duration_days']} days ({study_plan['start_date']} to {study_plan['end_date']})
- **Daily Study Time**: {study_plan['daily_hours']} hours
- **Total Study Hours**: {study_plan['total_hours']} hours
- **Subjects**: {', '.join(study_plan['subjects'])}

**Weekly Schedule Preview:**
"""
        
        # Show first week as example
        for i, session in enumerate(study_plan['daily_schedule'][:7]):
            day_name = session.date.strftime("%A")
            response += f"- **{day_name} ({session.date})**: {session.subject} - {session.unit} ({session.duration_hours}h)\n"
        
        if len(study_plan['daily_schedule']) > 7:
            response += f"... and {len(study_plan['daily_schedule']) - 7} more days\n"
        
        response += """
**Study Tips:**
1. 📚 Start each session by reviewing previous day's notes
2. ⏰ Take 10-minute breaks every hour
3. 📝 Make summary notes for quick revision
4. 🎯 Focus on understanding concepts, not just memorizing
5. 📊 Track your progress daily

**Next Steps:**
- I'll remind you about your daily study sessions
- Use "check progress" to see how you're doing
- Say "reschedule" if you need to adjust your plan

Good luck with your studies! 🌟
"""
        
        return response
    
    def _check_progress(self, user_id: str = None) -> Dict[str, Any]:
        """Check study progress for a user"""
        
        if not user_id:
            return self._format_success_response(
                "To check your progress, I need to know who you are. Please provide your user ID or log in."
            )
        
        db = get_db_session()
        try:
            # Get active study plans
            active_plans = db.query(StudyPlan).filter(
                StudyPlan.user_id == user_id,
                StudyPlan.status == "active"
            ).all()
            
            if not active_plans:
                return self._format_success_response(
                    "You don't have any active study plans. Would you like me to create one for you?"
                )
            
            progress_report = "**📊 Your Study Progress Report**\n\n"
            
            for plan in active_plans:
                # Get tasks for this plan
                tasks = db.query(StudyTask).filter(
                    StudyTask.study_plan_id == plan.id
                ).all()
                
                total_tasks = len(tasks)
                completed_tasks = len([t for t in tasks if t.status == "completed"])
                pending_tasks = len([t for t in tasks if t.status == "pending"])
                overdue_tasks = len([t for t in tasks if t.status == "pending" and t.scheduled_date < datetime.now()])
                
                progress_percentage = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
                
                progress_report += f"**{plan.title}**\n"
                progress_report += f"- Progress: {progress_percentage:.1f}% ({completed_tasks}/{total_tasks} tasks)\n"
                progress_report += f"- Pending: {pending_tasks} tasks\n"
                progress_report += f"- Overdue: {overdue_tasks} tasks\n"
                
                if overdue_tasks > 0:
                    progress_report += f"⚠️ You have {overdue_tasks} overdue tasks. Consider rescheduling!\n"
                
                progress_report += "\n"
            
            # Add motivational message
            if progress_percentage >= 80:
                progress_report += "🎉 Excellent progress! Keep up the great work!"
            elif progress_percentage >= 60:
                progress_report += "👍 Good progress! You're on track!"
            elif progress_percentage >= 40:
                progress_report += "💪 You're making progress! Stay consistent!"
            else:
                progress_report += "🚀 Let's get back on track! You can do this!"
            
            metadata = {
                "total_plans": len(active_plans),
                "overall_progress": progress_percentage
            }
            
            return self._format_success_response(progress_report, metadata)
            
        finally:
            db.close()
    
    def _get_daily_schedule(self, context: Dict[str, Any], user_id: str = None) -> Dict[str, Any]:
        """Get today's study schedule"""
        
        if not user_id:
            return self._format_success_response(
                "To show your daily schedule, I need to know who you are. Please provide your user ID."
            )
        
        target_date = context.get("date", datetime.now().date())
        if isinstance(target_date, str):
            target_date = datetime.strptime(target_date, "%Y-%m-%d").date()
        
        db = get_db_session()
        try:
            # Get today's tasks
            tasks = db.query(StudyTask).join(StudyPlan).filter(
                StudyPlan.user_id == user_id,
                StudyTask.scheduled_date >= datetime.combine(target_date, datetime.min.time()),
                StudyTask.scheduled_date < datetime.combine(target_date + timedelta(days=1), datetime.min.time())
            ).all()
            
            if not tasks:
                return self._format_success_response(
                    f"No study sessions scheduled for {target_date}. "
                    f"Would you like me to create a study plan for you?"
                )
            
            schedule_text = f"**📅 Study Schedule for {target_date.strftime('%A, %B %d, %Y')}**\n\n"
            
            total_hours = 0
            for task in tasks:
                status_emoji = "✅" if task.status == "completed" else "📚"
                schedule_text += f"{status_emoji} **{task.subject}** - {task.unit}\n"
                schedule_text += f"   Duration: {task.estimated_hours} hours\n"
                schedule_text += f"   Status: {task.status.title()}\n\n"
                total_hours += task.estimated_hours or 0
            
            schedule_text += f"**Total Study Time**: {total_hours} hours\n\n"
            schedule_text += "💡 **Tips for Today:**\n"
            schedule_text += "- Start with the most challenging subject\n"
            schedule_text += "- Take regular breaks\n"
            schedule_text += "- Review yesterday's notes first\n"
            schedule_text += "- End with a quick summary of what you learned"
            
            metadata = {
                "date": target_date.isoformat(),
                "total_tasks": len(tasks),
                "total_hours": total_hours,
                "completed_tasks": len([t for t in tasks if t.status == "completed"])
            }
            
            return self._format_success_response(schedule_text, metadata)
            
        finally:
            db.close()
    
    def _general_planning_help(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle general planning queries"""
        
        response = """
**📚 Study Planning Assistant**

I can help you with:

1. **Create Study Plans**: 
   - "Create a study plan for MBA subjects"
   - "Plan my studies for the next 2 months"

2. **Daily Schedules**:
   - "What should I study today?"
   - "Show my schedule for tomorrow"

3. **Progress Tracking**:
   - "Check my progress"
   - "How am I doing with my studies?"

4. **Rescheduling**:
   - "Reschedule missed topics"
   - "I missed yesterday's session"

**To create a personalized plan, tell me:**
- Your subjects/courses
- Available study time per day
- Exam dates (if any)
- Any priority topics

How can I help you organize your studies better?
"""
        
        return self._format_success_response(response)
    
    def _get_capabilities(self) -> List[str]:
        """Get list of agent capabilities"""
        return [
            "Create personalized daily/weekly study plans",
            "Break down syllabus into manageable chunks",
            "Track study progress and completion",
            "Automatically reschedule missed topics",
            "Provide daily study schedules",
            "Optimize study time based on exam dates",
            "Send study reminders and notifications"
        ]
