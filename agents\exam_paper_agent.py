"""
Exam Paper Agent - Previous Year Guru for IGNOU Study <PERSON><PERSON>
Handles exam paper related queries, answer generation, and question prediction
"""
import re
import json
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from datetime import datetime, timedelta
import requests
from bs4 import BeautifulSoup

from agents.base_agent import BaseAgent, AgentResponse
from core.vector_store import vector_store
from core.llm_integration import PromptTemplates
from core.database import get_db_session, Document as DBDocument
from config.settings import settings
from utils.logger import logger


class ExamPaperAgent(BaseAgent):
    """
    Exam Paper Agent that helps with previous year questions and exam preparation.
    
    Capabilities:
    - Search and retrieve previous year question papers
    - Generate answers for exam questions using textbook content
    - Predict likely questions for upcoming exams
    - Analyze question patterns and trends
    - Provide exam preparation strategies
    """
    
    def __init__(self):
        super().__init__("ExamPaperAgent")
        self.vector_store = vector_store
        self.exam_paper_sources = [
            "https://www.ignou.ac.in/ignou/studentzone/results/6",  # IGNOU official
            # Add more sources as needed
        ]
    
    def process_query(self, query: str, context: Dict[str, Any] = None, 
                     user_id: str = None) -> Dict[str, Any]:
        """Process exam paper related queries"""
        
        if not self._validate_input(query):
            return self._format_error_response("Invalid query provided")
        
        try:
            context = context or {}
            course = context.get("course")
            subject = context.get("subject")
            year = context.get("year")
            
            # Determine query type
            query_type = self._classify_exam_query(query)
            
            if query_type == "search_papers":
                return self._search_exam_papers(query, course, subject, year)
            elif query_type == "answer_question":
                return self._answer_exam_question(query, course, subject)
            elif query_type == "predict_questions":
                return self._predict_questions(course, subject)
            elif query_type == "exam_strategy":
                return self._provide_exam_strategy(query, course, subject)
            else:
                return self._general_exam_help(query, context)
                
        except Exception as e:
            logger.error(f"Error in exam paper agent: {e}")
            return self._format_error_response(str(e))
    
    def _classify_exam_query(self, query: str) -> str:
        """Classify the type of exam-related query"""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ["find", "search", "get", "download", "paper"]):
            return "search_papers"
        elif any(word in query_lower for word in ["answer", "solve", "explain", "solution"]):
            return "answer_question"
        elif any(word in query_lower for word in ["predict", "expected", "likely", "important"]):
            return "predict_questions"
        elif any(word in query_lower for word in ["strategy", "prepare", "tips", "approach"]):
            return "exam_strategy"
        else:
            return "general"
    
    def _search_exam_papers(self, query: str, course: str = None, 
                           subject: str = None, year: int = None) -> Dict[str, Any]:
        """Search for exam papers"""
        
        try:
            # First, check local database
            local_papers = self._search_local_papers(course, subject, year)
            
            # If no local papers found, try web scraping
            if not local_papers:
                web_papers = self._scrape_exam_papers(course, subject, year)
                if web_papers:
                    # Store scraped papers locally
                    self._store_exam_papers(web_papers)
                    local_papers = web_papers
            
            if not local_papers:
                return self._format_success_response(
                    f"I couldn't find exam papers for the specified criteria. "
                    f"Course: {course or 'Any'}, Subject: {subject or 'Any'}, Year: {year or 'Any'}. "
                    f"Please try with different search terms or check the IGNOU official website."
                )
            
            # Format response
            papers_info = []
            for paper in local_papers[:5]:  # Limit to top 5 results
                papers_info.append({
                    "title": paper.get("title", "Unknown"),
                    "course": paper.get("course", "Unknown"),
                    "subject": paper.get("subject", "Unknown"),
                    "year": paper.get("year", "Unknown"),
                    "file_path": paper.get("file_path", ""),
                    "source": paper.get("source", "Local")
                })
            
            response_text = "Here are the exam papers I found:\n\n"
            for i, paper in enumerate(papers_info, 1):
                response_text += f"{i}. **{paper['title']}**\n"
                response_text += f"   - Course: {paper['course']}\n"
                response_text += f"   - Subject: {paper['subject']}\n"
                response_text += f"   - Year: {paper['year']}\n"
                if paper['file_path']:
                    response_text += f"   - Available locally\n"
                response_text += "\n"
            
            metadata = {
                "papers_found": len(papers_info),
                "search_criteria": {
                    "course": course,
                    "subject": subject,
                    "year": year
                }
            }
            
            return self._format_success_response(response_text, metadata)
            
        except Exception as e:
            logger.error(f"Error searching exam papers: {e}")
            return self._format_error_response("Failed to search for exam papers")
    
    def _search_local_papers(self, course: str = None, subject: str = None, 
                            year: int = None) -> List[Dict[str, Any]]:
        """Search for exam papers in local database"""
        
        db = get_db_session()
        try:
            query = db.query(DBDocument).filter(
                DBDocument.document_type == "exam_paper"
            )
            
            if course:
                query = query.filter(DBDocument.course.ilike(f"%{course}%"))
            if subject:
                query = query.filter(DBDocument.subject.ilike(f"%{subject}%"))
            if year:
                query = query.filter(DBDocument.year == year)
            
            documents = query.all()
            
            return [
                {
                    "id": doc.id,
                    "title": doc.title,
                    "course": doc.course,
                    "subject": doc.subject,
                    "year": doc.year,
                    "file_path": doc.file_path,
                    "source": "Local Database"
                }
                for doc in documents
            ]
        finally:
            db.close()
    
    def _scrape_exam_papers(self, course: str = None, subject: str = None, 
                           year: int = None) -> List[Dict[str, Any]]:
        """Scrape exam papers from web sources"""
        
        papers = []
        
        # This is a simplified example - in practice, you'd need to implement
        # specific scrapers for different IGNOU websites
        try:
            # Example scraping logic (you'll need to adapt this to actual IGNOU sites)
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            # Note: This is a placeholder - actual implementation would need
            # to handle IGNOU's specific website structure
            logger.info("Web scraping for exam papers is not fully implemented yet")
            
        except Exception as e:
            logger.error(f"Error scraping exam papers: {e}")
        
        return papers
    
    def _answer_exam_question(self, question: str, course: str = None, 
                             subject: str = None) -> Dict[str, Any]:
        """Generate answer for an exam question using textbook content"""
        
        try:
            # Search for relevant content in textbooks
            search_query = question
            if course:
                search_query = f"Course {course}: {question}"
            if subject:
                search_query = f"Subject {subject}: {search_query}"
            
            search_results = self.vector_store.similarity_search(
                search_query, k=5, score_threshold=0.3
            )
            
            if not search_results:
                return self._format_success_response(
                    "I couldn't find relevant textbook content to answer this question. "
                    "Please make sure the relevant textbooks are uploaded to the system."
                )
            
            # Generate answer using RAG
            answer = self._generate_exam_answer(question, search_results, course, subject)
            
            metadata = {
                "question": question,
                "sources_used": len(search_results),
                "course": course,
                "subject": subject
            }
            
            return self._format_success_response(answer, metadata)
            
        except Exception as e:
            logger.error(f"Error answering exam question: {e}")
            return self._format_error_response("Failed to generate answer")
    
    def _generate_exam_answer(self, question: str, search_results: List[Dict[str, Any]], 
                             course: str = None, subject: str = None) -> str:
        """Generate a comprehensive exam answer"""
        
        # Prepare context from search results
        context_parts = []
        for i, result in enumerate(search_results[:3]):
            content = result["content"]
            metadata = result.get("metadata", {})
            source_info = f"Reference {i+1}"
            
            if metadata.get("course"):
                source_info += f" (Course: {metadata['course']}"
            if metadata.get("subject"):
                source_info += f", Subject: {metadata['subject']}"
            if metadata.get("page_number"):
                source_info += f", Page: {metadata['page_number']}"
            if metadata.get("course") or metadata.get("subject") or metadata.get("page_number"):
                source_info += ")"
            
            context_parts.append(f"{source_info}:\n{content}\n")
        
        context = "\n".join(context_parts)
        
        # System prompt for exam answers
        system_prompt = """
        You are an expert IGNOU exam answer writer. Your task is to write comprehensive,
        well-structured answers for IGNOU exam questions based on the provided textbook content.
        
        Guidelines for exam answers:
        - Start with a clear introduction that defines key terms
        - Organize the answer in logical sections with headings
        - Provide detailed explanations with examples where relevant
        - Include relevant diagrams or flowcharts descriptions if applicable
        - Conclude with a summary of key points
        - Aim for answers that would score full marks in IGNOU exams
        - Use formal academic language appropriate for university-level exams
        """
        
        user_prompt = f"""
Based on the following textbook content, write a comprehensive exam answer for the given question.

TEXTBOOK CONTENT:
{context}

EXAM QUESTION:
{question}

Please write a detailed, well-structured answer that would be suitable for an IGNOU exam.
Include proper headings, explanations, and examples where relevant.
"""
        
        try:
            response = self.llm_manager.generate_response(
                user_prompt, 
                system_prompt=system_prompt
            )
            return response
        except Exception as e:
            logger.error(f"Error generating exam answer: {e}")
            return "I'm having trouble generating a comprehensive answer right now."
    
    def _predict_questions(self, course: str, subject: str) -> Dict[str, Any]:
        """Predict likely questions for upcoming exams"""
        
        try:
            # This would analyze previous year papers to identify patterns
            # For now, providing a general response
            
            response = f"""
Based on analysis of previous year papers for {course} - {subject}, here are some likely question types:

**High Probability Topics:**
1. Fundamental concepts and definitions
2. Theoretical frameworks and models
3. Case study analysis
4. Comparative analysis between different approaches
5. Application-based questions

**Question Pattern Analysis:**
- Long answer questions (10-15 marks): Usually 3-4 questions
- Medium answer questions (5-7 marks): Usually 4-6 questions  
- Short answer questions (2-3 marks): Usually 6-8 questions

**Preparation Strategy:**
1. Focus on core concepts from each unit
2. Practice previous year questions
3. Prepare case studies and examples
4. Review important diagrams and flowcharts

Note: This is a general prediction. For more specific predictions, I would need access to more previous year papers for analysis.
"""
            
            metadata = {
                "course": course,
                "subject": subject,
                "prediction_type": "general_pattern"
            }
            
            return self._format_success_response(response, metadata)
            
        except Exception as e:
            logger.error(f"Error predicting questions: {e}")
            return self._format_error_response("Failed to predict questions")
    
    def _provide_exam_strategy(self, query: str, course: str = None, 
                              subject: str = None) -> Dict[str, Any]:
        """Provide exam preparation strategy"""
        
        strategy = f"""
**IGNOU Exam Preparation Strategy**

**Time Management:**
- Start preparation at least 2 months before exams
- Allocate 2-3 hours daily for focused study
- Reserve last 2 weeks for intensive revision

**Study Approach:**
1. **Unit-wise Study**: Complete one unit at a time
2. **Note Making**: Create concise notes for quick revision
3. **Practice Questions**: Solve previous year papers regularly
4. **Mock Tests**: Take timed practice tests

**Answer Writing Tips:**
- Read questions carefully and plan your answer
- Use proper headings and subheadings
- Include relevant examples and case studies
- Write legibly and maintain good presentation
- Manage time effectively (allocate time per question)

**Subject-specific Tips:**
{f"For {subject}:" if subject else "General tips:"}
- Focus on conceptual understanding over memorization
- Practice diagrams and flowcharts
- Prepare current examples and case studies
- Review assignment questions and solutions

**Final Week Strategy:**
- Quick revision using notes
- Practice time management with mock tests
- Stay calm and confident
- Ensure all required materials are ready
"""
        
        metadata = {
            "strategy_type": "comprehensive",
            "course": course,
            "subject": subject
        }
        
        return self._format_success_response(strategy, metadata)
    
    def _general_exam_help(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle general exam-related queries"""
        
        response = """
I'm here to help with your IGNOU exam preparation! I can assist you with:

1. **Finding Previous Year Papers**: Search for question papers by course, subject, or year
2. **Answering Exam Questions**: Get detailed answers based on textbook content
3. **Question Prediction**: Analyze patterns to predict likely questions
4. **Exam Strategy**: Get personalized preparation and answer writing tips

Please specify what you need help with, and include details like:
- Course code (e.g., IGNOU MBA, BCA, etc.)
- Subject name
- Specific year (if looking for papers)
- Your specific question or topic

How can I help you prepare better for your exams?
"""
        
        return self._format_success_response(response)
    
    def _store_exam_papers(self, papers: List[Dict[str, Any]]):
        """Store scraped exam papers in the database"""
        
        db = get_db_session()
        try:
            for paper in papers:
                existing = db.query(DBDocument).filter(
                    DBDocument.title == paper.get("title"),
                    DBDocument.course == paper.get("course"),
                    DBDocument.year == paper.get("year")
                ).first()
                
                if not existing:
                    db_document = DBDocument(
                        title=paper.get("title"),
                        file_path=paper.get("file_path", ""),
                        document_type="exam_paper",
                        course=paper.get("course"),
                        subject=paper.get("subject"),
                        year=paper.get("year"),
                        file_size=0,  # Will be updated when file is downloaded
                        processed=False
                    )
                    db.add(db_document)
            
            db.commit()
            
        except Exception as e:
            logger.error(f"Error storing exam papers: {e}")
            db.rollback()
        finally:
            db.close()
    
    def _get_capabilities(self) -> List[str]:
        """Get list of agent capabilities"""
        return [
            "Search previous year question papers",
            "Generate detailed answers for exam questions",
            "Predict likely questions for upcoming exams",
            "Analyze question patterns and trends",
            "Provide exam preparation strategies",
            "Offer answer writing tips and techniques"
        ]
