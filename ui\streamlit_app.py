"""
Streamlit Web Interface for IGNOU Study Bot
"""
import streamlit as st
import json
from datetime import datetime, date
from typing import Dict, Any, List
import pandas as pd

from core.orchestrator import orchestrator
from core.database import init_database, get_db_session, User, StudyPlan, QuizAttempt
from agents import TextbookAgent, ExamPaperAgent, StudyPlannerAgent, QuizMasterAgent, MentorAgent
from config.settings import settings
from utils.logger import logger


def init_streamlit():
    """Initialize Streamlit configuration"""
    st.set_page_config(
        page_title="IGNOU Study Bot",
        page_icon="🎓",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Custom CSS
    st.markdown("""
    <style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .agent-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .success-message {
        background-color: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .error-message {
        background-color: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    </style>
    """, unsafe_allow_html=True)


def initialize_session_state():
    """Initialize Streamlit session state"""
    if 'user_id' not in st.session_state:
        st.session_state.user_id = "demo_user"
    
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    
    if 'current_quiz' not in st.session_state:
        st.session_state.current_quiz = None
    
    if 'quiz_answers' not in st.session_state:
        st.session_state.quiz_answers = {}


def main():
    """Main Streamlit application"""
    init_streamlit()
    initialize_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">🎓 IGNOU Study Bot</h1>', unsafe_allow_html=True)
    st.markdown("*Your AI-Powered Multi-Agent Study Assistant*")
    
    # Sidebar
    with st.sidebar:
        st.header("🎯 Navigation")
        
        page = st.selectbox(
            "Choose a page:",
            ["🏠 Home", "💬 Chat", "📚 Textbook Helper", "📝 Exam Papers", 
             "📅 Study Planner", "🧪 Quiz Master", "🧠 Mentor", "📊 Dashboard"]
        )
        
        st.divider()
        
        # User info
        st.header("👤 User Info")
        user_id = st.text_input("User ID", value=st.session_state.user_id)
        if user_id != st.session_state.user_id:
            st.session_state.user_id = user_id
        
        # Quick stats
        if st.button("📊 Quick Stats"):
            show_quick_stats()
    
    # Main content based on selected page
    if page == "🏠 Home":
        show_home_page()
    elif page == "💬 Chat":
        show_chat_page()
    elif page == "📚 Textbook Helper":
        show_textbook_page()
    elif page == "📝 Exam Papers":
        show_exam_papers_page()
    elif page == "📅 Study Planner":
        show_study_planner_page()
    elif page == "🧪 Quiz Master":
        show_quiz_master_page()
    elif page == "🧠 Mentor":
        show_mentor_page()
    elif page == "📊 Dashboard":
        show_dashboard_page()


def show_home_page():
    """Show home page"""
    st.header("Welcome to Your Personal IGNOU Study Assistant! 🌟")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("🤖 Meet Your AI Agents")
        
        agents_info = [
            ("📚 Textbook Agent", "Your personal tutor for textbook questions and explanations"),
            ("📝 Exam Paper Agent", "Previous year papers and exam preparation expert"),
            ("📅 Study Planner", "Personalized study schedules and time management"),
            ("🧪 Quiz Master", "Interactive quizzes and knowledge testing"),
            ("🧠 Mentor", "Progress tracking, motivation, and guidance")
        ]
        
        for name, description in agents_info:
            st.markdown(f"""
            <div class="agent-card">
                <h4>{name}</h4>
                <p>{description}</p>
            </div>
            """, unsafe_allow_html=True)
    
    with col2:
        st.subheader("🚀 Quick Actions")
        
        if st.button("📚 Ask a Textbook Question", use_container_width=True):
            st.session_state.page = "📚 Textbook Helper"
            st.rerun()

        if st.button("📝 Find Exam Papers", use_container_width=True):
            st.session_state.page = "📝 Exam Papers"
            st.rerun()

        if st.button("📅 Create Study Plan", use_container_width=True):
            st.session_state.page = "📅 Study Planner"
            st.rerun()

        if st.button("🧪 Take a Quiz", use_container_width=True):
            st.session_state.page = "🧪 Quiz Master"
            st.rerun()

        if st.button("📊 View Progress", use_container_width=True):
            st.session_state.page = "📊 Dashboard"
            st.rerun()
    
    # Recent activity
    st.subheader("📈 Recent Activity")
    show_recent_activity()


def show_chat_page():
    """Show chat interface"""
    st.header("💬 Chat with Your Study Bot")
    
    # Chat history
    chat_container = st.container()
    
    with chat_container:
        for message in st.session_state.chat_history:
            if message["role"] == "user":
                st.chat_message("user").write(message["content"])
            else:
                st.chat_message("assistant").write(message["content"])
    
    # Chat input
    if prompt := st.chat_input("Ask me anything about your studies..."):
        # Add user message to history
        st.session_state.chat_history.append({"role": "user", "content": prompt})
        
        # Show user message
        st.chat_message("user").write(prompt)
        
        # Get response from orchestrator
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                response = orchestrator.process_query(
                    prompt, 
                    user_id=st.session_state.user_id
                )
                
                if response["success"]:
                    st.write(response["response"])
                    
                    # Show metadata if available
                    if "metadata" in response:
                        with st.expander("ℹ️ Details"):
                            st.json(response["metadata"])
                else:
                    st.error("Sorry, I encountered an error processing your request.")
                
                # Add assistant response to history
                st.session_state.chat_history.append({
                    "role": "assistant", 
                    "content": response["response"]
                })


def show_textbook_page():
    """Show textbook helper page"""
    st.header("📚 Textbook Helper")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("Ask a Question")
        
        question = st.text_area(
            "What would you like to know from your textbooks?",
            placeholder="e.g., Explain the concept of database normalization"
        )
        
        # Context options
        with st.expander("🔧 Advanced Options"):
            course = st.text_input("Course (optional)", placeholder="e.g., MCA, MBA")
            subject = st.text_input("Subject (optional)", placeholder="e.g., Database Management")
            language = st.selectbox("Response Language", ["English", "Hindi", "Tamil"])
        
        if st.button("🔍 Get Answer", type="primary"):
            if question:
                context = {
                    "course": course,
                    "subject": subject,
                    "language": language.lower()
                }
                
                with st.spinner("Searching textbooks..."):
                    agent = TextbookAgent()
                    response = agent.process_query(question, context, st.session_state.user_id)
                    
                    if response["success"]:
                        st.success("Answer found!")
                        st.markdown(response["answer"])
                        
                        if "metadata" in response:
                            with st.expander("📖 Sources"):
                                st.json(response["metadata"])
                    else:
                        st.error(response.get("error", "Failed to get answer"))
            else:
                st.warning("Please enter a question")
    
    with col2:
        st.subheader("📖 Available Textbooks")
        
        agent = TextbookAgent()
        textbooks = agent.list_available_textbooks()
        
        if textbooks:
            for book in textbooks:
                st.markdown(f"""
                **{book['title']}**
                - Course: {book['course']}
                - Subject: {book['subject']}
                - Pages: {book['page_count']}
                """)
        else:
            st.info("No textbooks uploaded yet. Upload PDFs to get started!")
        
        # Upload textbook
        st.subheader("📤 Upload Textbook")
        uploaded_file = st.file_uploader("Choose PDF file", type="pdf")
        
        if uploaded_file:
            course_input = st.text_input("Course Code", key="upload_course")
            subject_input = st.text_input("Subject Name", key="upload_subject")
            
            if st.button("Upload & Process"):
                if course_input and subject_input:
                    # Save uploaded file temporarily
                    import tempfile
                    with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
                        tmp_file.write(uploaded_file.getvalue())
                        tmp_path = tmp_file.name
                    
                    with st.spinner("Processing PDF..."):
                        result = agent.add_textbook(
                            tmp_path, course_input, subject_input, uploaded_file.name
                        )
                        
                        if result["success"]:
                            st.success(f"Successfully processed {result['chunks_count']} chunks!")
                        else:
                            st.error(result["error"])
                else:
                    st.warning("Please provide course and subject information")


def show_quiz_master_page():
    """Show quiz master page"""
    st.header("🧪 Quiz Master")
    
    tab1, tab2, tab3 = st.tabs(["📝 Take Quiz", "📊 Results", "🎯 Practice"])
    
    with tab1:
        st.subheader("Generate a New Quiz")
        
        col1, col2 = st.columns(2)
        
        with col1:
            topic = st.text_input("Topic/Subject", placeholder="e.g., Database Management")
            num_questions = st.slider("Number of Questions", 5, 20, 10)
        
        with col2:
            difficulty = st.selectbox("Difficulty", ["Easy", "Medium", "Hard"])
            question_type = st.selectbox("Question Type", ["Multiple Choice"])
        
        if st.button("🎲 Generate Quiz", type="primary"):
            if topic:
                context = {
                    "topic": topic,
                    "num_questions": num_questions,
                    "difficulty": difficulty.lower()
                }
                
                with st.spinner("Generating quiz..."):
                    agent = QuizMasterAgent()
                    response = agent.process_query(
                        f"Generate a {num_questions}-question quiz on {topic}",
                        context,
                        st.session_state.user_id
                    )
                    
                    if response["success"]:
                        st.success("Quiz generated!")
                        st.markdown(response["answer"])
                        
                        # Store quiz info for answering
                        if "metadata" in response:
                            st.session_state.current_quiz = response["metadata"]
                    else:
                        st.error("Failed to generate quiz")
            else:
                st.warning("Please enter a topic")
        
        # Answer submission
        if st.session_state.current_quiz:
            st.subheader("Submit Your Answers")
            
            quiz_id = st.session_state.current_quiz.get("quiz_id")
            if quiz_id:
                answers_input = st.text_input(
                    "Your Answers (e.g., A, B, C, D, A)",
                    placeholder="Enter your answers separated by commas"
                )
                
                if st.button("📤 Submit Answers"):
                    if answers_input:
                        # Parse answers
                        try:
                            answers = [ord(ans.strip().upper()) - ord('A') for ans in answers_input.split(',')]
                            
                            context = {
                                "quiz_id": quiz_id,
                                "answers": answers
                            }
                            
                            with st.spinner("Evaluating..."):
                                response = agent.process_query(
                                    "Submit quiz answers",
                                    context,
                                    st.session_state.user_id
                                )
                                
                                if response["success"]:
                                    st.success("Quiz evaluated!")
                                    st.markdown(response["answer"])
                                    st.session_state.current_quiz = None
                                else:
                                    st.error("Failed to evaluate quiz")
                        except:
                            st.error("Invalid answer format. Use: A, B, C, D, A")
                    else:
                        st.warning("Please enter your answers")
    
    with tab2:
        st.subheader("📊 Quiz Performance")
        
        if st.button("📈 Show Performance Report"):
            agent = QuizMasterAgent()
            response = agent.process_query(
                "Show my quiz performance",
                {},
                st.session_state.user_id
            )
            
            if response["success"]:
                st.markdown(response["answer"])
            else:
                st.info("No quiz history found")
    
    with tab3:
        st.subheader("🎯 Practice Mode")
        st.info("Practice mode coming soon! Generate random questions for continuous learning.")


def show_mentor_page():
    """Show mentor page"""
    st.header("🧠 Your Personal Mentor")
    
    tab1, tab2, tab3 = st.tabs(["📊 Progress", "🏆 Achievements", "💪 Motivation"])
    
    with tab1:
        st.subheader("📈 Progress Report")
        
        if st.button("📊 Generate Progress Report", type="primary"):
            agent = MentorAgent()
            response = agent.process_query(
                "Show my progress report",
                {},
                st.session_state.user_id
            )
            
            if response["success"]:
                st.markdown(response["answer"])
                
                if "metadata" in response:
                    col1, col2, col3, col4 = st.columns(4)
                    
                    with col1:
                        st.metric("Level", response["metadata"].get("level", 1))
                    with col2:
                        st.metric("Points", response["metadata"].get("points", 0))
                    with col3:
                        st.metric("Study Streak", f"{response['metadata'].get('study_streak', 0)} days")
                    with col4:
                        st.metric("Completion Rate", f"{response['metadata'].get('completion_rate', 0):.1f}%")
            else:
                st.error("Failed to generate progress report")
    
    with tab2:
        st.subheader("🏆 Achievements & Badges")
        
        if st.button("🏆 Show My Achievements"):
            agent = MentorAgent()
            response = agent.process_query(
                "Show my achievements",
                {},
                st.session_state.user_id
            )
            
            if response["success"]:
                st.markdown(response["answer"])
            else:
                st.error("Failed to load achievements")
    
    with tab3:
        st.subheader("💪 Motivation & Support")
        
        motivation_request = st.text_area(
            "How are you feeling about your studies?",
            placeholder="e.g., I'm feeling unmotivated today..."
        )
        
        if st.button("💫 Get Motivation"):
            if motivation_request:
                agent = MentorAgent()
                response = agent.process_query(
                    motivation_request,
                    {},
                    st.session_state.user_id
                )
                
                if response["success"]:
                    st.markdown(response["answer"])
                else:
                    st.error("Failed to generate motivation")
            else:
                # Default motivation
                st.markdown("""
                🌟 **You're doing great!**
                
                Remember, every expert was once a beginner. Every small step you take is building toward your success.
                
                - 📚 Consistency beats perfection
                - 🎯 Progress, not perfection, is the goal
                - 💪 You're stronger than you think
                - 🚀 Your future self will thank you for not giving up
                
                Keep going! 💫
                """)


def show_dashboard_page():
    """Show analytics dashboard"""
    st.header("📊 Analytics Dashboard")
    
    # This would show comprehensive analytics
    st.info("Comprehensive analytics dashboard coming soon!")
    
    # Placeholder metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Study Hours", "45.5", "2.5")
    with col2:
        st.metric("Completed Tasks", "23", "3")
    with col3:
        st.metric("Quiz Average", "78%", "5%")
    with col4:
        st.metric("Study Streak", "7 days", "1")


def show_quick_stats():
    """Show quick stats in sidebar"""
    try:
        # This would fetch real stats from database
        st.sidebar.metric("Study Streak", "7 days")
        st.sidebar.metric("Level", "3")
        st.sidebar.metric("Points", "245")
    except Exception as e:
        st.sidebar.error("Could not load stats")


def show_recent_activity():
    """Show recent activity"""
    # Placeholder recent activity
    activities = [
        "📚 Completed Database Normalization study session",
        "🧪 Scored 85% on SQL Quiz",
        "📝 Found exam papers for MCA Semester 3",
        "📅 Updated study plan for next week"
    ]
    
    for activity in activities:
        st.markdown(f"• {activity}")


# Additional helper functions for other pages would go here...
def show_study_planner_page():
    """Show study planner page"""
    st.header("📅 Study Planner")
    st.info("Study planner interface coming soon!")


def show_exam_papers_page():
    """Show exam papers page"""
    st.header("📝 Exam Papers")
    st.info("Exam papers interface coming soon!")


if __name__ == "__main__":
    # Initialize database
    try:
        init_database()
    except Exception as e:
        st.error(f"Database initialization failed: {e}")
    
    main()
