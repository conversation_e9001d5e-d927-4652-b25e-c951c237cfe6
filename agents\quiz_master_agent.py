"""
Quiz Master Agent - Evaluator for IGNOU Study Bot
Handles quiz generation, scoring, and performance tracking
"""
import json
import random
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass

from agents.base_agent import BaseAgent, AgentResponse
from core.vector_store import vector_store
from core.llm_integration import PromptTemplates
from core.database import get_db_session, Quiz, QuizAttempt, User
from config.settings import settings
from utils.logger import logger


@dataclass
class Question:
    """Represents a quiz question"""
    question: str
    options: List[str]
    correct_answer: int  # Index of correct option (0-based)
    explanation: str
    difficulty: str = "medium"
    topic: str = ""


@dataclass
class QuizResult:
    """Represents quiz results"""
    score: float
    total_questions: int
    correct_answers: int
    time_taken: int
    questions: List[Question]
    user_answers: List[int]
    detailed_feedback: List[str]


class QuizMasterAgent(BaseAgent):
    """
    Quiz Master Agent that generates and evaluates quizzes.
    
    Capabilities:
    - Generate MCQ quizzes from textbook content
    - Create subject-specific and topic-specific quizzes
    - Evaluate quiz responses and provide scores
    - Track performance over time
    - Identify weak areas for focused study
    - Provide detailed explanations for answers
    """
    
    def __init__(self):
        super().__init__("QuizMasterAgent")
        self.vector_store = vector_store
        self.default_quiz_length = settings.default_quiz_questions
    
    def process_query(self, query: str, context: Dict[str, Any] = None, 
                     user_id: str = None) -> Dict[str, Any]:
        """Process quiz-related queries"""
        
        if not self._validate_input(query):
            return self._format_error_response("Invalid query provided")
        
        try:
            context = context or {}
            
            # Determine query type
            query_type = self._classify_quiz_query(query)
            
            if query_type == "generate_quiz":
                return self._generate_quiz(query, context, user_id)
            elif query_type == "submit_answers":
                return self._evaluate_quiz(query, context, user_id)
            elif query_type == "performance_report":
                return self._get_performance_report(user_id)
            elif query_type == "weak_areas":
                return self._identify_weak_areas(user_id)
            elif query_type == "practice_mode":
                return self._start_practice_mode(query, context, user_id)
            else:
                return self._general_quiz_help(query, context)
                
        except Exception as e:
            logger.error(f"Error in quiz master agent: {e}")
            return self._format_error_response(str(e))
    
    def _classify_quiz_query(self, query: str) -> str:
        """Classify the type of quiz query"""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ["generate", "create", "make", "quiz", "test"]):
            return "generate_quiz"
        elif any(word in query_lower for word in ["submit", "answer", "my answers", "evaluate"]):
            return "submit_answers"
        elif any(word in query_lower for word in ["performance", "report", "results", "scores"]):
            return "performance_report"
        elif any(word in query_lower for word in ["weak", "areas", "improve", "focus"]):
            return "weak_areas"
        elif any(word in query_lower for word in ["practice", "random", "mixed"]):
            return "practice_mode"
        else:
            return "general"
    
    def _generate_quiz(self, query: str, context: Dict[str, Any], 
                      user_id: str = None) -> Dict[str, Any]:
        """Generate a quiz based on the request"""
        
        try:
            # Extract quiz parameters
            quiz_params = self._extract_quiz_parameters(query, context)
            
            if not quiz_params.get("topic") and not quiz_params.get("subject"):
                return self._format_success_response(
                    "To generate a quiz, I need to know:\n\n"
                    "1. **Subject/Topic**: What subject or topic should the quiz cover?\n"
                    "2. **Number of Questions**: How many questions? (default: 10)\n"
                    "3. **Difficulty**: Easy, Medium, or Hard? (default: Medium)\n\n"
                    "Example: 'Generate a 10-question quiz on Database Management Systems'"
                )
            
            # Search for relevant content
            search_results = self._search_quiz_content(quiz_params)
            
            if not search_results:
                return self._format_success_response(
                    f"I couldn't find enough content to generate a quiz on {quiz_params.get('topic', quiz_params.get('subject'))}. "
                    f"Please make sure the relevant textbooks are uploaded to the system."
                )
            
            # Generate questions
            questions = self._generate_questions(search_results, quiz_params)
            
            if not questions:
                return self._format_error_response("Failed to generate quiz questions")
            
            # Save quiz to database
            quiz_id = self._save_quiz(questions, quiz_params, user_id)
            
            # Format response
            response = self._format_quiz_response(questions, quiz_id)
            
            metadata = {
                "quiz_id": quiz_id,
                "question_count": len(questions),
                "difficulty": quiz_params.get("difficulty", "medium"),
                "topic": quiz_params.get("topic", quiz_params.get("subject"))
            }
            
            return self._format_success_response(response, metadata)
            
        except Exception as e:
            logger.error(f"Error generating quiz: {e}")
            return self._format_error_response("Failed to generate quiz")
    
    def _extract_quiz_parameters(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract quiz parameters from query and context"""
        
        # Use LLM to extract structured information
        extraction_prompt = f"""
        Extract quiz parameters from the following query. Return a JSON object with these fields:
        - topic: specific topic mentioned (if any)
        - subject: broader subject mentioned (if any)
        - num_questions: number of questions requested (default: 10)
        - difficulty: easy/medium/hard (default: medium)
        - question_type: mcq/short/long (default: mcq)
        
        Query: {query}
        Context: {json.dumps(context)}
        
        Return only valid JSON:
        """
        
        try:
            response = self.llm_manager.generate_response(extraction_prompt)
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                params = json.loads(json_match.group())
                return params
        except Exception as e:
            logger.warning(f"Failed to extract quiz parameters with LLM: {e}")
        
        # Fallback extraction
        return {
            "topic": context.get("topic", ""),
            "subject": context.get("subject", ""),
            "num_questions": context.get("num_questions", self.default_quiz_length),
            "difficulty": context.get("difficulty", "medium"),
            "question_type": "mcq"
        }
    
    def _search_quiz_content(self, quiz_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Search for content to generate quiz questions"""
        
        search_query = quiz_params.get("topic") or quiz_params.get("subject", "")
        num_questions = quiz_params.get("num_questions", self.default_quiz_length)
        
        # Search for more content than needed to have variety
        search_results = self.vector_store.similarity_search(
            search_query, 
            k=num_questions * 3,  # Get 3x more content for variety
            score_threshold=0.3
        )
        
        return search_results
    
    def _generate_questions(self, search_results: List[Dict[str, Any]], 
                           quiz_params: Dict[str, Any]) -> List[Question]:
        """Generate quiz questions from search results"""
        
        num_questions = quiz_params.get("num_questions", self.default_quiz_length)
        difficulty = quiz_params.get("difficulty", "medium")
        topic = quiz_params.get("topic", quiz_params.get("subject", ""))
        
        questions = []
        
        # Group search results into chunks for question generation
        content_chunks = []
        for result in search_results[:num_questions * 2]:  # Use 2x content for variety
            content_chunks.append(result["content"])
        
        # Generate questions in batches
        batch_size = 3
        for i in range(0, min(len(content_chunks), num_questions), batch_size):
            batch_content = content_chunks[i:i+batch_size]
            batch_questions = self._generate_question_batch(batch_content, difficulty, topic)
            questions.extend(batch_questions)
            
            if len(questions) >= num_questions:
                break
        
        # Trim to requested number and shuffle
        questions = questions[:num_questions]
        random.shuffle(questions)
        
        return questions
    
    def _generate_question_batch(self, content_chunks: List[str], 
                                difficulty: str, topic: str) -> List[Question]:
        """Generate a batch of questions from content chunks"""
        
        content = "\n\n".join(content_chunks)
        
        system_prompt = PromptTemplates.QUIZ_GENERATION_SYSTEM
        
        user_prompt = f"""
Based on the following content, generate 2-3 multiple choice questions.

CONTENT:
{content}

REQUIREMENTS:
- Difficulty: {difficulty}
- Topic: {topic}
- Each question should have 4 options (A, B, C, D)
- Only one correct answer per question
- Include brief explanations for correct answers
- Focus on understanding, not just memorization

Return the questions in this JSON format:
[
  {{
    "question": "Question text here?",
    "options": ["Option A", "Option B", "Option C", "Option D"],
    "correct_answer": 0,
    "explanation": "Explanation for the correct answer",
    "difficulty": "{difficulty}",
    "topic": "{topic}"
  }}
]
"""
        
        try:
            response = self.llm_manager.generate_response(user_prompt, system_prompt)
            
            # Extract JSON from response
            import re
            json_match = re.search(r'\[.*\]', response, re.DOTALL)
            if json_match:
                questions_data = json.loads(json_match.group())
                
                questions = []
                for q_data in questions_data:
                    question = Question(
                        question=q_data["question"],
                        options=q_data["options"],
                        correct_answer=q_data["correct_answer"],
                        explanation=q_data["explanation"],
                        difficulty=q_data.get("difficulty", difficulty),
                        topic=q_data.get("topic", topic)
                    )
                    questions.append(question)
                
                return questions
                
        except Exception as e:
            logger.error(f"Error generating question batch: {e}")
        
        return []
    
    def _save_quiz(self, questions: List[Question], quiz_params: Dict[str, Any], 
                   user_id: str = None) -> int:
        """Save quiz to database"""
        
        db = get_db_session()
        try:
            # Convert questions to JSON format
            questions_json = []
            for q in questions:
                questions_json.append({
                    "question": q.question,
                    "options": q.options,
                    "correct_answer": q.correct_answer,
                    "explanation": q.explanation,
                    "difficulty": q.difficulty,
                    "topic": q.topic
                })
            
            quiz = Quiz(
                title=f"Quiz: {quiz_params.get('topic', quiz_params.get('subject', 'General'))}",
                subject=quiz_params.get("subject", ""),
                unit=quiz_params.get("topic", ""),
                difficulty=quiz_params.get("difficulty", "medium"),
                questions=questions_json
            )
            
            db.add(quiz)
            db.commit()
            
            return quiz.id
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error saving quiz: {e}")
            raise
        finally:
            db.close()
    
    def _format_quiz_response(self, questions: List[Question], quiz_id: int) -> str:
        """Format quiz as a readable response"""
        
        response = f"**🧪 Quiz Generated Successfully!**\n\n"
        response += f"**Quiz ID**: {quiz_id}\n"
        response += f"**Questions**: {len(questions)}\n"
        response += f"**Instructions**: Choose the best answer for each question.\n\n"
        
        for i, question in enumerate(questions, 1):
            response += f"**Question {i}**: {question.question}\n\n"
            
            for j, option in enumerate(question.options):
                letter = chr(65 + j)  # A, B, C, D
                response += f"   {letter}) {option}\n"
            
            response += "\n"
        
        response += "**To submit your answers**, reply with your choices like: 'My answers: A, B, C, D, A...'\n"
        response += f"**Quiz ID for submission**: {quiz_id}"
        
        return response
    
    def _evaluate_quiz(self, query: str, context: Dict[str, Any], 
                      user_id: str = None) -> Dict[str, Any]:
        """Evaluate quiz answers"""
        
        try:
            # Extract quiz ID and answers from query/context
            quiz_id = context.get("quiz_id")
            user_answers = context.get("answers", [])
            
            if not quiz_id:
                return self._format_success_response(
                    "Please provide the Quiz ID and your answers in this format:\n"
                    "'Quiz ID: 123, My answers: A, B, C, D, A'"
                )
            
            # Get quiz from database
            db = get_db_session()
            try:
                quiz = db.query(Quiz).filter(Quiz.id == quiz_id).first()
                if not quiz:
                    return self._format_error_response("Quiz not found")
                
                # Evaluate answers
                result = self._calculate_quiz_results(quiz, user_answers)
                
                # Save attempt to database
                attempt_id = self._save_quiz_attempt(quiz_id, user_id, result)
                
                # Format results
                response = self._format_quiz_results(result, attempt_id)
                
                metadata = {
                    "quiz_id": quiz_id,
                    "attempt_id": attempt_id,
                    "score": result.score,
                    "total_questions": result.total_questions
                }
                
                return self._format_success_response(response, metadata)
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error evaluating quiz: {e}")
            return self._format_error_response("Failed to evaluate quiz")
    
    def _calculate_quiz_results(self, quiz: Quiz, user_answers: List[int]) -> QuizResult:
        """Calculate quiz results"""
        
        questions_data = quiz.questions
        questions = []
        
        for q_data in questions_data:
            question = Question(
                question=q_data["question"],
                options=q_data["options"],
                correct_answer=q_data["correct_answer"],
                explanation=q_data["explanation"],
                difficulty=q_data.get("difficulty", "medium"),
                topic=q_data.get("topic", "")
            )
            questions.append(question)
        
        correct_answers = 0
        detailed_feedback = []
        
        for i, (question, user_answer) in enumerate(zip(questions, user_answers)):
            is_correct = user_answer == question.correct_answer
            if is_correct:
                correct_answers += 1
            
            feedback = f"Q{i+1}: {'✅ Correct' if is_correct else '❌ Incorrect'}"
            if not is_correct:
                correct_letter = chr(65 + question.correct_answer)
                feedback += f" (Correct answer: {correct_letter})"
            feedback += f" - {question.explanation}"
            detailed_feedback.append(feedback)
        
        score = (correct_answers / len(questions)) * 100 if questions else 0
        
        return QuizResult(
            score=score,
            total_questions=len(questions),
            correct_answers=correct_answers,
            time_taken=0,  # Would be calculated from actual timing
            questions=questions,
            user_answers=user_answers,
            detailed_feedback=detailed_feedback
        )
    
    def _format_quiz_results(self, result: QuizResult, attempt_id: int) -> str:
        """Format quiz results as readable response"""
        
        response = f"**🎯 Quiz Results**\n\n"
        response += f"**Score**: {result.score:.1f}% ({result.correct_answers}/{result.total_questions})\n"
        response += f"**Attempt ID**: {attempt_id}\n\n"
        
        # Performance message
        if result.score >= 90:
            response += "🌟 **Excellent!** Outstanding performance!\n"
        elif result.score >= 80:
            response += "👍 **Great job!** Very good understanding!\n"
        elif result.score >= 70:
            response += "✅ **Good work!** You're on the right track!\n"
        elif result.score >= 60:
            response += "📚 **Keep studying!** You're making progress!\n"
        else:
            response += "💪 **Don't give up!** Review the topics and try again!\n"
        
        response += "\n**Detailed Feedback:**\n\n"
        for feedback in result.detailed_feedback:
            response += f"{feedback}\n\n"
        
        response += "**Next Steps:**\n"
        response += "- Review incorrect answers and explanations\n"
        response += "- Study the topics you missed\n"
        response += "- Take another quiz to test your improvement\n"
        response += "- Check your performance report with 'show my quiz performance'"
        
        return response
    
    def _save_quiz_attempt(self, quiz_id: int, user_id: str, result: QuizResult) -> int:
        """Save quiz attempt to database"""
        
        db = get_db_session()
        try:
            attempt = QuizAttempt(
                user_id=user_id,
                quiz_id=quiz_id,
                answers=result.user_answers,
                score=result.score,
                total_questions=result.total_questions,
                time_taken=result.time_taken
            )
            
            db.add(attempt)
            db.commit()
            
            return attempt.id
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error saving quiz attempt: {e}")
            raise
        finally:
            db.close()
    
    def _get_performance_report(self, user_id: str = None) -> Dict[str, Any]:
        """Get user's quiz performance report"""
        
        if not user_id:
            return self._format_success_response(
                "To show your performance report, I need to know who you are. Please provide your user ID."
            )
        
        db = get_db_session()
        try:
            attempts = db.query(QuizAttempt).filter(
                QuizAttempt.user_id == user_id
            ).order_by(QuizAttempt.completed_at.desc()).all()
            
            if not attempts:
                return self._format_success_response(
                    "You haven't taken any quizzes yet. Would you like me to generate one for you?"
                )
            
            # Calculate statistics
            total_attempts = len(attempts)
            average_score = sum(attempt.score for attempt in attempts) / total_attempts
            best_score = max(attempt.score for attempt in attempts)
            recent_attempts = attempts[:5]  # Last 5 attempts
            
            response = f"**📊 Your Quiz Performance Report**\n\n"
            response += f"**Overall Statistics:**\n"
            response += f"- Total Quizzes Taken: {total_attempts}\n"
            response += f"- Average Score: {average_score:.1f}%\n"
            response += f"- Best Score: {best_score:.1f}%\n\n"
            
            response += f"**Recent Performance:**\n"
            for i, attempt in enumerate(recent_attempts, 1):
                quiz = db.query(Quiz).filter(Quiz.id == attempt.quiz_id).first()
                quiz_title = quiz.title if quiz else f"Quiz {attempt.quiz_id}"
                response += f"{i}. {quiz_title}: {attempt.score:.1f}% ({attempt.completed_at.strftime('%Y-%m-%d')})\n"
            
            # Performance trend
            if len(attempts) >= 3:
                recent_avg = sum(attempt.score for attempt in attempts[:3]) / 3
                older_avg = sum(attempt.score for attempt in attempts[-3:]) / 3
                
                if recent_avg > older_avg + 5:
                    response += "\n📈 **Trend**: Your performance is improving! Keep it up!"
                elif recent_avg < older_avg - 5:
                    response += "\n📉 **Trend**: Consider reviewing your study approach."
                else:
                    response += "\n📊 **Trend**: Your performance is consistent."
            
            metadata = {
                "total_attempts": total_attempts,
                "average_score": average_score,
                "best_score": best_score
            }
            
            return self._format_success_response(response, metadata)
            
        finally:
            db.close()
    
    def _general_quiz_help(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle general quiz queries"""
        
        response = """
**🧪 Quiz Master Assistant**

I can help you with:

1. **Generate Quizzes**:
   - "Create a quiz on Database Management"
   - "Generate 15 questions on Marketing"

2. **Take Quizzes**:
   - Answer the questions I provide
   - Submit with: "My answers: A, B, C, D..."

3. **Performance Tracking**:
   - "Show my quiz performance"
   - "What are my weak areas?"

4. **Practice Modes**:
   - "Random practice questions"
   - "Mixed difficulty quiz"

**Quiz Features:**
- Multiple choice questions from your textbooks
- Instant scoring and feedback
- Performance tracking over time
- Weak area identification
- Difficulty levels: Easy, Medium, Hard

Ready to test your knowledge? Just tell me what topic you'd like to practice!
"""
        
        return self._format_success_response(response)
    
    def _get_capabilities(self) -> List[str]:
        """Get list of agent capabilities"""
        return [
            "Generate MCQ quizzes from textbook content",
            "Create subject-specific and topic-specific quizzes",
            "Evaluate quiz responses with detailed feedback",
            "Track performance and progress over time",
            "Identify weak areas for focused study",
            "Provide explanations for correct answers",
            "Support multiple difficulty levels"
        ]
