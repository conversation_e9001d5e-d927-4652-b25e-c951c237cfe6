"""
Base agent class for IGNOU Study Bot agents
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime

from utils.logger import logger
from core.llm_integration import llm_manager
from core.database import get_db_session


class BaseAgent(ABC):
    """Base class for all study bot agents"""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.llm_manager = llm_manager
        logger.info(f"{self.agent_name} agent initialized")
    
    @abstractmethod
    def process_query(self, query: str, context: Dict[str, Any] = None, 
                     user_id: str = None) -> Dict[str, Any]:
        """Process a user query and return a response"""
        pass
    
    def _log_interaction(self, query: str, response: Dict[str, Any], 
                        user_id: str = None):
        """Log the interaction for analytics and improvement"""
        try:
            # In a production system, you might want to store this in a separate analytics table
            logger.info(f"Agent: {self.agent_name}, Query: {query[:100]}..., "
                       f"Success: {response.get('success', False)}")
        except Exception as e:
            logger.error(f"Failed to log interaction: {e}")
    
    def _validate_input(self, query: str) -> bool:
        """Validate input query"""
        if not query or not query.strip():
            return False
        if len(query) > 10000:  # Reasonable limit
            return False
        return True
    
    def _format_error_response(self, error_message: str) -> Dict[str, Any]:
        """Format error response"""
        return {
            "answer": f"I'm sorry, I encountered an error: {error_message}",
            "success": False,
            "error": error_message,
            "timestamp": datetime.utcnow().isoformat(),
            "agent": self.agent_name
        }
    
    def _format_success_response(self, answer: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """Format successful response"""
        response = {
            "answer": answer,
            "success": True,
            "timestamp": datetime.utcnow().isoformat(),
            "agent": self.agent_name
        }
        
        if metadata:
            response["metadata"] = metadata
        
        return response
    
    def get_agent_info(self) -> Dict[str, Any]:
        """Get information about this agent"""
        return {
            "name": self.agent_name,
            "description": self.__doc__ or "No description available",
            "capabilities": self._get_capabilities(),
            "status": "active"
        }
    
    @abstractmethod
    def _get_capabilities(self) -> List[str]:
        """Get list of agent capabilities"""
        pass


class AgentResponse:
    """Standardized response format for agents"""
    
    def __init__(self, success: bool, answer: str, metadata: Dict[str, Any] = None,
                 error: str = None, agent_name: str = None):
        self.success = success
        self.answer = answer
        self.metadata = metadata or {}
        self.error = error
        self.agent_name = agent_name
        self.timestamp = datetime.utcnow().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = {
            "success": self.success,
            "answer": self.answer,
            "timestamp": self.timestamp
        }
        
        if self.metadata:
            result["metadata"] = self.metadata
        
        if self.error:
            result["error"] = self.error
        
        if self.agent_name:
            result["agent"] = self.agent_name
        
        return result
    
    @classmethod
    def success(cls, answer: str, metadata: Dict[str, Any] = None, 
                agent_name: str = None) -> 'AgentResponse':
        """Create successful response"""
        return cls(True, answer, metadata, None, agent_name)
    
    @classmethod
    def error(cls, error_message: str, agent_name: str = None) -> 'AgentResponse':
        """Create error response"""
        return cls(False, f"I'm sorry, I encountered an error: {error_message}", 
                  None, error_message, agent_name)
