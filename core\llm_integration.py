"""
LLM integration for IGNOU Study Bot
Supports OpenAI, Google AI, and Anthropic models
"""
from typing import Optional, Dict, Any, List
from abc import ABC, abstractmethod

from langchain.llms.base import LLM
from langchain_openai import ChatOpenAI
from langchain.schema import BaseMessage, HumanMessage, SystemMessage, AIMessage

from config.settings import settings
from utils.logger import logger


class BaseLLMProvider(ABC):
    """Base class for LLM providers"""
    
    @abstractmethod
    def generate_response(self, prompt: str, system_prompt: str = None, **kwargs) -> str:
        """Generate response from the LLM"""
        pass
    
    @abstractmethod
    def generate_chat_response(self, messages: List[BaseMessage], **kwargs) -> str:
        """Generate response from chat messages"""
        pass


class OpenAIProvider(BaseLLMProvider):
    """OpenAI LLM provider"""
    
    def __init__(self, api_key: str = None, model: str = None):
        self.api_key = api_key or settings.openai_api_key
        self.model = model or settings.default_model
        
        if not self.api_key:
            raise ValueError("OpenAI API key not provided")
        
        self.llm = ChatOpenAI(
            openai_api_key=self.api_key,
            model_name=self.model,
            temperature=settings.temperature,
            max_tokens=settings.max_tokens
        )
        
        logger.info(f"OpenAI provider initialized with model: {self.model}")
    
    def generate_response(self, prompt: str, system_prompt: str = None, **kwargs) -> str:
        """Generate response from the LLM"""
        messages = []
        
        if system_prompt:
            messages.append(SystemMessage(content=system_prompt))
        
        messages.append(HumanMessage(content=prompt))
        
        try:
            response = self.llm(messages, **kwargs)
            return response.content
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise
    
    def generate_chat_response(self, messages: List[BaseMessage], **kwargs) -> str:
        """Generate response from chat messages"""
        try:
            response = self.llm(messages, **kwargs)
            return response.content
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise


class GoogleAIProvider(BaseLLMProvider):
    """Google AI LLM provider"""
    
    def __init__(self, api_key: str = None, model: str = None):
        self.api_key = api_key or settings.google_ai_api_key
        self.model = model or "gemini-pro"
        
        if not self.api_key:
            raise ValueError("Google AI API key not provided")
        
        try:
            import google.generativeai as genai
            genai.configure(api_key=self.api_key)
            self.client = genai.GenerativeModel(self.model)
            logger.info(f"Google AI provider initialized with model: {self.model}")
        except ImportError:
            raise ImportError("google-generativeai package not installed")
    
    def generate_response(self, prompt: str, system_prompt: str = None, **kwargs) -> str:
        """Generate response from the LLM"""
        full_prompt = prompt
        if system_prompt:
            full_prompt = f"{system_prompt}\n\n{prompt}"
        
        try:
            response = self.client.generate_content(full_prompt)
            return response.text
        except Exception as e:
            logger.error(f"Google AI API error: {e}")
            raise
    
    def generate_chat_response(self, messages: List[BaseMessage], **kwargs) -> str:
        """Generate response from chat messages"""
        # Convert messages to a single prompt for Gemini
        prompt_parts = []
        for message in messages:
            if isinstance(message, SystemMessage):
                prompt_parts.append(f"System: {message.content}")
            elif isinstance(message, HumanMessage):
                prompt_parts.append(f"Human: {message.content}")
            elif isinstance(message, AIMessage):
                prompt_parts.append(f"Assistant: {message.content}")
        
        full_prompt = "\n".join(prompt_parts)
        return self.generate_response(full_prompt, **kwargs)


class LLMManager:
    """Manager for different LLM providers"""
    
    def __init__(self, default_provider: str = None):
        self.default_provider = default_provider or settings.default_llm_provider
        self.providers: Dict[str, BaseLLMProvider] = {}
        
        # Initialize available providers
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize available LLM providers"""
        # OpenAI
        if settings.openai_api_key:
            try:
                self.providers['openai'] = OpenAIProvider()
                logger.info("OpenAI provider initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI provider: {e}")
        
        # Google AI
        if settings.google_ai_api_key:
            try:
                self.providers['google'] = GoogleAIProvider()
                logger.info("Google AI provider initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Google AI provider: {e}")
        
        if not self.providers:
            logger.error("No LLM providers available. Please configure API keys.")
    
    def get_provider(self, provider_name: str = None) -> BaseLLMProvider:
        """Get LLM provider by name"""
        provider_name = provider_name or self.default_provider
        
        if provider_name not in self.providers:
            available = list(self.providers.keys())
            if available:
                provider_name = available[0]
                logger.warning(f"Provider {provider_name} not available, using {provider_name}")
            else:
                raise ValueError("No LLM providers available")
        
        return self.providers[provider_name]
    
    def generate_response(self, prompt: str, system_prompt: str = None, 
                         provider: str = None, **kwargs) -> str:
        """Generate response using specified or default provider"""
        llm_provider = self.get_provider(provider)
        return llm_provider.generate_response(prompt, system_prompt, **kwargs)
    
    def generate_chat_response(self, messages: List[BaseMessage], 
                              provider: str = None, **kwargs) -> str:
        """Generate chat response using specified or default provider"""
        llm_provider = self.get_provider(provider)
        return llm_provider.generate_chat_response(messages, **kwargs)
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers"""
        return list(self.providers.keys())


# Predefined prompts for different agents
class PromptTemplates:
    """Collection of prompt templates for different agents"""
    
    TEXTBOOK_QA_SYSTEM = """
    You are an expert IGNOU tutor with deep knowledge of IGNOU textbooks and curriculum.
    Your role is to help students understand concepts from their textbooks by providing clear,
    accurate explanations based on the provided context.
    
    Guidelines:
    - Always base your answers on the provided textbook content
    - Explain concepts in simple, easy-to-understand language
    - Provide examples when helpful
    - If the context doesn't contain enough information, say so clearly
    - Support multilingual explanations when requested (Tamil, Hindi, English)
    """
    
    QUIZ_GENERATION_SYSTEM = """
    You are a quiz generator for IGNOU students. Create high-quality multiple-choice questions
    based on the provided textbook content.
    
    Guidelines:
    - Generate questions that test understanding, not just memorization
    - Include 4 options with only one correct answer
    - Make distractors plausible but clearly incorrect
    - Vary difficulty levels appropriately
    - Focus on key concepts and important topics
    """
    
    STUDY_PLANNER_SYSTEM = """
    You are a study planning expert for IGNOU students. Help create personalized,
    realistic study schedules based on available time and syllabus requirements.
    
    Guidelines:
    - Consider the student's available time and constraints
    - Break down large topics into manageable chunks
    - Include time for revision and practice
    - Be realistic about time estimates
    - Prioritize important topics for exams
    """
    
    MOTIVATIONAL_SYSTEM = """
    You are a supportive mentor for IGNOU students. Provide encouragement,
    motivation, and guidance to help students stay on track with their studies.
    
    Guidelines:
    - Be positive and encouraging
    - Acknowledge progress and achievements
    - Provide practical advice for overcoming challenges
    - Use gamification elements when appropriate
    - Be empathetic to student struggles
    """


# Global LLM manager instance
llm_manager = LLMManager()
