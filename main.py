"""
Main entry point for IGNOU Study Bot
"""
import asyncio
import argparse
from pathlib import Path

from utils.logger import logger
from config.settings import settings


def main():
    """Main function to run the IGNOU Study Bot"""
    parser = argparse.ArgumentParser(description="IGNOU Study Bot - AI-Powered Multi-Agent Study Assistant")
    parser.add_argument("--mode", choices=["cli", "web", "telegram"], default="cli", 
                       help="Interface mode (default: cli)")
    parser.add_argument("--agent", choices=["textbook", "exam", "planner", "quiz", "mentor"], 
                       help="Run specific agent")
    parser.add_argument("--setup", action="store_true", help="Run initial setup")
    
    args = parser.parse_args()
    
    logger.info("Starting IGNOU Study Bot...")
    logger.info(f"Mode: {args.mode}")
    
    if args.setup:
        run_setup()
        return
    
    if args.mode == "cli":
        run_cli_interface(args.agent)
    elif args.mode == "web":
        run_web_interface()
    elif args.mode == "telegram":
        run_telegram_bot()


def run_setup():
    """Run initial setup for the application"""
    logger.info("Running initial setup...")
    
    # Ensure directories exist
    settings.ensure_directories()
    logger.info("✓ Created required directories")
    
    # Check API keys
    api_keys_status = check_api_keys()
    if not any(api_keys_status.values()):
        logger.warning("⚠️  No API keys configured. Please set up your API keys in .env file")
    else:
        logger.info("✓ API keys configured")
    
    # Initialize database
    try:
        from core.database import init_database
        init_database()
        logger.info("✓ Database initialized")
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
    
    logger.info("Setup completed!")


def check_api_keys():
    """Check which API keys are configured"""
    return {
        "openai": bool(settings.openai_api_key),
        "google": bool(settings.google_ai_api_key),
        "anthropic": bool(settings.anthropic_api_key),
        "serpapi": bool(settings.serpapi_key),
        "tavily": bool(settings.tavily_api_key),
    }


def run_cli_interface(specific_agent=None):
    """Run the command-line interface"""
    logger.info("Starting CLI interface...")
    
    if specific_agent:
        logger.info(f"Running specific agent: {specific_agent}")
        # TODO: Implement specific agent running
    else:
        # TODO: Implement main CLI loop
        print("🎓 Welcome to IGNOU Study Bot!")
        print("Available commands:")
        print("  1. Ask textbook questions")
        print("  2. Get study plan")
        print("  3. Take a quiz")
        print("  4. Check progress")
        print("  5. Find exam papers")
        print("  Type 'exit' to quit")
        
        while True:
            try:
                user_input = input("\n> ").strip()
                if user_input.lower() in ['exit', 'quit']:
                    break
                
                # TODO: Process user input and route to appropriate agent
                print(f"Processing: {user_input}")
                
            except KeyboardInterrupt:
                print("\nGoodbye!")
                break


def run_web_interface():
    """Run the Streamlit web interface"""
    logger.info("Starting web interface...")
    import subprocess
    import sys
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "ui/streamlit_app.py", 
            "--server.port", str(settings.streamlit_port)
        ])
    except Exception as e:
        logger.error(f"Failed to start web interface: {e}")


def run_telegram_bot():
    """Run the Telegram bot interface"""
    logger.info("Starting Telegram bot...")
    
    if not settings.telegram_bot_token:
        logger.error("Telegram bot token not configured")
        return
    
    try:
        from ui.telegram_bot import run_bot
        asyncio.run(run_bot())
    except Exception as e:
        logger.error(f"Failed to start Telegram bot: {e}")


if __name__ == "__main__":
    main()
