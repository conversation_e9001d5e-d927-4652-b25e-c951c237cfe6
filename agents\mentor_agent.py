"""
Mentor Agent - Motivator and Progress Tracker for IGNOU Study Bot
Handles motivation, progress tracking, and gamification
"""
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from agents.base_agent import BaseAgent, AgentResponse
from core.database import (get_db_session, User, StudyPlan, StudyTask, 
                          QuizAttempt, ProgressRecord)
from core.llm_integration import PromptTemplates
from config.settings import settings
from utils.logger import logger


class AchievementType(Enum):
    """Types of achievements"""
    STUDY_STREAK = "study_streak"
    QUIZ_MASTER = "quiz_master"
    EARLY_BIRD = "early_bird"
    NIGHT_OWL = "night_owl"
    CONSISTENT_LEARNER = "consistent_learner"
    IMPROVEMENT = "improvement"
    COMPLETION = "completion"


@dataclass
class Achievement:
    """Represents a user achievement"""
    type: AchievementType
    title: str
    description: str
    icon: str
    earned_date: datetime
    points: int = 10


@dataclass
class ProgressStats:
    """User progress statistics"""
    total_study_hours: float
    completed_tasks: int
    pending_tasks: int
    quiz_average: float
    study_streak: int
    achievements: List[Achievement]
    level: int
    points: int


class MentorAgent(BaseAgent):
    """
    Mentor Agent that provides motivation, tracks progress, and gamifies learning.
    
    Capabilities:
    - Track overall study progress and milestones
    - Provide motivational messages and encouragement
    - Gamify learning with points, levels, and achievements
    - Identify areas needing attention
    - Send study reminders and celebrations
    - Generate progress reports and analytics
    """
    
    def __init__(self):
        super().__init__("MentorAgent")
        self.points_per_task = 10
        self.points_per_quiz = 20
        self.points_per_streak_day = 5
    
    def process_query(self, query: str, context: Dict[str, Any] = None, 
                     user_id: str = None) -> Dict[str, Any]:
        """Process mentor-related queries"""
        
        if not self._validate_input(query):
            return self._format_error_response("Invalid query provided")
        
        try:
            context = context or {}
            
            # Determine query type
            query_type = self._classify_mentor_query(query)
            
            if query_type == "progress_report":
                return self._generate_progress_report(user_id)
            elif query_type == "motivation":
                return self._provide_motivation(query, context, user_id)
            elif query_type == "achievements":
                return self._show_achievements(user_id)
            elif query_type == "goals":
                return self._manage_goals(query, context, user_id)
            elif query_type == "analytics":
                return self._provide_analytics(user_id)
            elif query_type == "celebration":
                return self._celebrate_milestone(query, context, user_id)
            else:
                return self._general_mentor_help(query, context, user_id)
                
        except Exception as e:
            logger.error(f"Error in mentor agent: {e}")
            return self._format_error_response(str(e))
    
    def _classify_mentor_query(self, query: str) -> str:
        """Classify the type of mentor query"""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ["progress", "report", "status", "overview"]):
            return "progress_report"
        elif any(word in query_lower for word in ["motivate", "encourage", "support", "help me"]):
            return "motivation"
        elif any(word in query_lower for word in ["achievement", "badge", "reward", "earned"]):
            return "achievements"
        elif any(word in query_lower for word in ["goal", "target", "objective", "aim"]):
            return "goals"
        elif any(word in query_lower for word in ["analytics", "statistics", "data", "trends"]):
            return "analytics"
        elif any(word in query_lower for word in ["celebrate", "completed", "finished", "done"]):
            return "celebration"
        else:
            return "general"
    
    def _generate_progress_report(self, user_id: str = None) -> Dict[str, Any]:
        """Generate comprehensive progress report"""
        
        if not user_id:
            return self._format_success_response(
                "To show your progress report, I need to know who you are. Please provide your user ID."
            )
        
        try:
            stats = self._calculate_progress_stats(user_id)
            
            # Generate motivational progress report
            report = self._format_progress_report(stats, user_id)
            
            # Check for new achievements
            new_achievements = self._check_new_achievements(user_id, stats)
            if new_achievements:
                report += "\n\n" + self._format_new_achievements(new_achievements)
            
            metadata = {
                "level": stats.level,
                "points": stats.points,
                "study_streak": stats.study_streak,
                "completion_rate": (stats.completed_tasks / (stats.completed_tasks + stats.pending_tasks) * 100) if (stats.completed_tasks + stats.pending_tasks) > 0 else 0
            }
            
            return self._format_success_response(report, metadata)
            
        except Exception as e:
            logger.error(f"Error generating progress report: {e}")
            return self._format_error_response("Failed to generate progress report")
    
    def _calculate_progress_stats(self, user_id: str) -> ProgressStats:
        """Calculate user progress statistics"""
        
        db = get_db_session()
        try:
            # Get study tasks
            completed_tasks = db.query(StudyTask).join(StudyPlan).filter(
                StudyPlan.user_id == user_id,
                StudyTask.status == "completed"
            ).count()
            
            pending_tasks = db.query(StudyTask).join(StudyPlan).filter(
                StudyPlan.user_id == user_id,
                StudyTask.status == "pending"
            ).count()
            
            # Calculate total study hours
            total_hours_result = db.query(StudyTask).join(StudyPlan).filter(
                StudyPlan.user_id == user_id,
                StudyTask.status == "completed",
                StudyTask.actual_hours.isnot(None)
            ).all()
            
            total_study_hours = sum(task.actual_hours or task.estimated_hours or 0 
                                  for task in total_hours_result)
            
            # Get quiz performance
            quiz_attempts = db.query(QuizAttempt).filter(
                QuizAttempt.user_id == user_id
            ).all()
            
            quiz_average = (sum(attempt.score for attempt in quiz_attempts) / len(quiz_attempts)) if quiz_attempts else 0
            
            # Calculate study streak
            study_streak = self._calculate_study_streak(user_id, db)
            
            # Get achievements
            achievements = self._get_user_achievements(user_id, db)
            
            # Calculate level and points
            points = self._calculate_total_points(user_id, db)
            level = self._calculate_level(points)
            
            return ProgressStats(
                total_study_hours=total_study_hours,
                completed_tasks=completed_tasks,
                pending_tasks=pending_tasks,
                quiz_average=quiz_average,
                study_streak=study_streak,
                achievements=achievements,
                level=level,
                points=points
            )
            
        finally:
            db.close()
    
    def _calculate_study_streak(self, user_id: str, db) -> int:
        """Calculate current study streak"""
        
        # Get recent completed tasks
        recent_tasks = db.query(StudyTask).join(StudyPlan).filter(
            StudyPlan.user_id == user_id,
            StudyTask.status == "completed",
            StudyTask.completed_date.isnot(None)
        ).order_by(StudyTask.completed_date.desc()).limit(30).all()
        
        if not recent_tasks:
            return 0
        
        # Count consecutive days with completed tasks
        streak = 0
        current_date = datetime.now().date()
        
        # Group tasks by date
        task_dates = set()
        for task in recent_tasks:
            if task.completed_date:
                task_dates.add(task.completed_date.date())
        
        # Count consecutive days
        while current_date in task_dates:
            streak += 1
            current_date -= timedelta(days=1)
        
        return streak
    
    def _get_user_achievements(self, user_id: str, db) -> List[Achievement]:
        """Get user achievements from database"""
        
        # In a real implementation, you'd have an achievements table
        # For now, return empty list
        return []
    
    def _calculate_total_points(self, user_id: str, db) -> int:
        """Calculate total points earned by user"""
        
        # Points from completed tasks
        completed_tasks = db.query(StudyTask).join(StudyPlan).filter(
            StudyPlan.user_id == user_id,
            StudyTask.status == "completed"
        ).count()
        
        task_points = completed_tasks * self.points_per_task
        
        # Points from quizzes
        quiz_attempts = db.query(QuizAttempt).filter(
            QuizAttempt.user_id == user_id
        ).count()
        
        quiz_points = quiz_attempts * self.points_per_quiz
        
        # Points from study streak
        streak = self._calculate_study_streak(user_id, db)
        streak_points = streak * self.points_per_streak_day
        
        return task_points + quiz_points + streak_points
    
    def _calculate_level(self, points: int) -> int:
        """Calculate user level based on points"""
        
        # Level progression: 100 points per level
        return max(1, points // 100)
    
    def _format_progress_report(self, stats: ProgressStats, user_id: str) -> str:
        """Format progress report as readable text"""
        
        report = f"**🎓 Your Learning Journey Report**\n\n"
        
        # Level and points
        report += f"**🏆 Level {stats.level}** ({stats.points} points)\n"
        next_level_points = (stats.level * 100) - stats.points
        if next_level_points > 0:
            report += f"*{next_level_points} points to next level*\n\n"
        else:
            report += f"*Ready to level up!*\n\n"
        
        # Study statistics
        report += f"**📚 Study Statistics:**\n"
        report += f"- Total Study Hours: {stats.total_study_hours:.1f} hours\n"
        report += f"- Completed Tasks: {stats.completed_tasks}\n"
        report += f"- Pending Tasks: {stats.pending_tasks}\n"
        
        if stats.completed_tasks + stats.pending_tasks > 0:
            completion_rate = (stats.completed_tasks / (stats.completed_tasks + stats.pending_tasks)) * 100
            report += f"- Completion Rate: {completion_rate:.1f}%\n"
        
        report += f"- Quiz Average: {stats.quiz_average:.1f}%\n"
        report += f"- Study Streak: {stats.study_streak} days 🔥\n\n"
        
        # Progress assessment
        report += "**📊 Progress Assessment:**\n"
        
        if stats.study_streak >= 7:
            report += "🌟 Amazing consistency! Your study streak is impressive!\n"
        elif stats.study_streak >= 3:
            report += "👍 Good momentum! Keep the streak going!\n"
        elif stats.study_streak == 0:
            report += "💪 Time to start a new streak! Every expert was once a beginner.\n"
        
        if stats.quiz_average >= 80:
            report += "🎯 Excellent quiz performance! You're mastering the material!\n"
        elif stats.quiz_average >= 60:
            report += "📈 Good quiz scores! Keep practicing to improve further!\n"
        elif stats.quiz_average > 0:
            report += "📚 Focus on understanding concepts better. Practice makes perfect!\n"
        
        # Motivational message
        report += "\n" + self._generate_motivational_message(stats)
        
        return report
    
    def _generate_motivational_message(self, stats: ProgressStats) -> str:
        """Generate personalized motivational message"""
        
        messages = []
        
        if stats.study_streak >= 10:
            messages.append("🔥 You're on fire! This consistency will lead to great results!")
        elif stats.study_streak >= 5:
            messages.append("⭐ Your dedication is showing! Keep up this excellent routine!")
        elif stats.completed_tasks >= 20:
            messages.append("🚀 Look at all those completed tasks! You're making real progress!")
        elif stats.quiz_average >= 85:
            messages.append("🧠 Your quiz performance shows you're really understanding the material!")
        else:
            messages.append("🌱 Every step forward is progress. You're building something great!")
        
        # Add encouraging next steps
        if stats.pending_tasks > 0:
            messages.append(f"📋 You have {stats.pending_tasks} tasks waiting. Each one completed brings you closer to your goals!")
        
        if stats.study_streak == 0:
            messages.append("🎯 Start a study session today and begin a new streak!")
        
        return "\n".join(messages)
    
    def _provide_motivation(self, query: str, context: Dict[str, Any], 
                           user_id: str = None) -> Dict[str, Any]:
        """Provide motivational support"""
        
        # Get user stats for personalized motivation
        stats = None
        if user_id:
            try:
                stats = self._calculate_progress_stats(user_id)
            except Exception as e:
                logger.warning(f"Could not get stats for motivation: {e}")
        
        # Generate personalized motivational response
        system_prompt = PromptTemplates.MOTIVATIONAL_SYSTEM
        
        user_prompt = f"""
The user is asking for motivation or support. Here's their query: "{query}"

User context:
{f"- Current level: {stats.level}" if stats else ""}
{f"- Study streak: {stats.study_streak} days" if stats else ""}
{f"- Completed tasks: {stats.completed_tasks}" if stats else ""}
{f"- Quiz average: {stats.quiz_average:.1f}%" if stats else ""}

Provide an encouraging, personalized response that:
1. Acknowledges their current situation
2. Provides specific motivation based on their progress
3. Offers practical next steps
4. Uses positive, supportive language
5. Includes relevant emojis for engagement
"""
        
        try:
            response = self.llm_manager.generate_response(user_prompt, system_prompt)
            
            # Add gamification elements
            if stats:
                if stats.study_streak >= 7:
                    response += f"\n\n🏆 **Achievement Unlocked**: Week Warrior! ({stats.study_streak} day streak)"
                elif stats.completed_tasks > 0 and stats.completed_tasks % 10 == 0:
                    response += f"\n\n🎉 **Milestone**: {stats.completed_tasks} tasks completed!"
            
            metadata = {
                "motivation_type": "personalized",
                "user_level": stats.level if stats else 1,
                "encouragement_level": "high"
            }
            
            return self._format_success_response(response, metadata)
            
        except Exception as e:
            logger.error(f"Error generating motivation: {e}")
            
            # Fallback motivational message
            fallback_message = """
🌟 **You're doing great!** 

Remember, every expert was once a beginner. Every small step you take is building toward your success. 

Here's what I want you to remember:
- 📚 Consistency beats perfection
- 🎯 Progress, not perfection, is the goal  
- 💪 You're stronger than you think
- 🚀 Your future self will thank you for not giving up

Take it one day at a time, one task at a time. You've got this! 💫
"""
            
            return self._format_success_response(fallback_message)
    
    def _show_achievements(self, user_id: str = None) -> Dict[str, Any]:
        """Show user achievements and badges"""
        
        if not user_id:
            return self._format_success_response(
                "To show your achievements, I need to know who you are. Please provide your user ID."
            )
        
        try:
            stats = self._calculate_progress_stats(user_id)
            
            response = f"**🏆 Your Achievements**\n\n"
            
            # Check for achievements based on stats
            earned_achievements = []
            
            if stats.study_streak >= 7:
                earned_achievements.append("🔥 **Week Warrior** - 7+ day study streak")
            if stats.study_streak >= 30:
                earned_achievements.append("🌟 **Consistency Champion** - 30+ day study streak")
            if stats.completed_tasks >= 10:
                earned_achievements.append("✅ **Task Master** - 10+ completed tasks")
            if stats.completed_tasks >= 50:
                earned_achievements.append("🚀 **Productivity Pro** - 50+ completed tasks")
            if stats.quiz_average >= 90:
                earned_achievements.append("🧠 **Quiz Genius** - 90%+ quiz average")
            if stats.total_study_hours >= 50:
                earned_achievements.append("📚 **Study Scholar** - 50+ study hours")
            if stats.level >= 5:
                earned_achievements.append("⭐ **Rising Star** - Reached Level 5")
            
            if earned_achievements:
                response += "\n".join(earned_achievements)
                response += f"\n\n**Total Achievements**: {len(earned_achievements)}"
            else:
                response += "🌱 **Getting Started!**\n\n"
                response += "You're just beginning your journey. Here are some achievements to work toward:\n\n"
                response += "🔥 **Week Warrior** - Study for 7 consecutive days\n"
                response += "✅ **Task Master** - Complete 10 study tasks\n"
                response += "🧠 **Quiz Ace** - Score 80%+ on a quiz\n"
                response += "📚 **Study Scholar** - Log 20+ study hours\n"
            
            response += f"\n\n**Current Level**: {stats.level} ({stats.points} points)"
            response += f"\n**Next Level**: {(stats.level * 100) - stats.points} points to go!"
            
            metadata = {
                "achievements_count": len(earned_achievements),
                "level": stats.level,
                "points": stats.points
            }
            
            return self._format_success_response(response, metadata)
            
        except Exception as e:
            logger.error(f"Error showing achievements: {e}")
            return self._format_error_response("Failed to load achievements")
    
    def _general_mentor_help(self, query: str, context: Dict[str, Any], 
                            user_id: str = None) -> Dict[str, Any]:
        """Handle general mentor queries"""
        
        response = """
**🧠 Your Personal Study Mentor**

I'm here to support and motivate you on your IGNOU journey! I can help with:

**📊 Progress Tracking:**
- "Show my progress report"
- "How am I doing?"
- "What's my study streak?"

**🏆 Achievements & Gamification:**
- "Show my achievements"
- "What level am I?"
- "How many points do I have?"

**💪 Motivation & Support:**
- "I'm feeling unmotivated"
- "Encourage me to study"
- "I'm struggling with my studies"

**📈 Analytics & Insights:**
- "Show my study analytics"
- "What are my weak areas?"
- "How can I improve?"

**🎉 Celebrations:**
- "I completed my assignment!"
- "I finished a difficult chapter!"

**Your Success is My Mission!** 🌟

I track your progress, celebrate your wins, and provide encouragement when you need it most. Together, we'll make your IGNOU journey successful and enjoyable!

What would you like help with today?
"""
        
        return self._format_success_response(response)
    
    def _get_capabilities(self) -> List[str]:
        """Get list of agent capabilities"""
        return [
            "Track overall study progress and milestones",
            "Provide personalized motivational messages",
            "Gamify learning with points, levels, and achievements",
            "Generate comprehensive progress reports",
            "Identify areas needing attention",
            "Celebrate milestones and accomplishments",
            "Provide study analytics and insights"
        ]
